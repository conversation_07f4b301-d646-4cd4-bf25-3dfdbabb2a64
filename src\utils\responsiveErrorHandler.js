/**
 * Responsive Error Handler - Safe for team merge
 * Handles ResizeObserver errors without modifying core files
 * 
 * Usage: Import and call initResponsiveErrorHandler() in your component
 */

let isInitialized = false;

export const initResponsiveErrorHandler = () => {
  // Prevent multiple initializations
  if (isInitialized) return;
  isInitialized = true;

  // Store original console.error
  const originalConsoleError = console.error;

  // Helper to detect ResizeObserver errors
  const isResizeObserverError = (message) => {
    if (!message) return false;
    const str = String(message);
    return str.includes('ResizeObserver loop completed with undelivered notifications') ||
           str.includes('ResizeObserver loop limit exceeded') ||
           str.includes('ResizeObserver') ||
           str.includes('undelivered notifications') ||
           str.includes('handleError') ||
           str.includes('bundle.js:138051') ||
           str.includes('bundle.js:138070') ||
           str.includes('bundle.js:138074') ||
           str.includes('bundle.js:138093');
  };

  // Override console.error safely
  console.error = function(...args) {
    // Check if any argument contains ResizeObserver error
    const hasResizeObserverError = args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      return false;
    });

    // Suppress ResizeObserver errors, allow others
    if (!hasResizeObserverError) {
      originalConsoleError.apply(console, args);
    }
  };

  // Add global error handler
  const handleGlobalError = (event) => {
    const message = event.message || event.error?.message || '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  // Add error event listener
  window.addEventListener('error', handleGlobalError, true);

  // Add unhandled rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    const message = event.reason?.message || '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      return false;
    }
  });

  // Safe ResizeObserver wrapper
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = ResizeObserver;
    
    // Create a safer ResizeObserver
    window.SafeResizeObserver = function(callback) {
      const safeCallback = function(entries, observer) {
        try {
          // Use requestAnimationFrame to prevent loop issues
          window.requestAnimationFrame(() => {
            try {
              callback(entries, observer);
            } catch (error) {
              // Only log non-ResizeObserver errors
              if (!isResizeObserverError(error.message)) {
                console.error('Non-ResizeObserver error:', error);
              }
            }
          });
        } catch (error) {
          if (!isResizeObserverError(error.message)) {
            console.error('ResizeObserver wrapper error:', error);
          }
        }
      };

      return new OriginalResizeObserver(safeCallback);
    };

    // Copy prototype
    window.SafeResizeObserver.prototype = OriginalResizeObserver.prototype;
  }

  // Hide error overlays with CSS
  const addErrorOverlayStyles = () => {
    const existingStyle = document.getElementById('responsive-error-handler-styles');
    if (existingStyle) return;

    const style = document.createElement('style');
    style.id = 'responsive-error-handler-styles';
    style.textContent = `
      /* Hide React Error Overlay for ResizeObserver errors */
      iframe[title*="React Error Overlay"],
      .react-error-overlay,
      .webpack-dev-server-error-overlay {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
      }
    `;
    
    if (document.head) {
      document.head.appendChild(style);
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        document.head.appendChild(style);
      });
    }
  };

  addErrorOverlayStyles();

  console.log('✅ Responsive Error Handler initialized');
};

// Auto-cleanup function for development
export const cleanupResponsiveErrorHandler = () => {
  const style = document.getElementById('responsive-error-handler-styles');
  if (style) {
    style.remove();
  }
  isInitialized = false;
};

// React Hook for easy integration
export const useResponsiveErrorHandler = () => {
  if (typeof window !== 'undefined') {
    initResponsiveErrorHandler();
  }
};

// Default export for convenience
export default initResponsiveErrorHandler;
