import React from "react";
import { deleteWarehouse } from "./khoHang<PERSON><PERSON>";
import { Button, Typography, message, Space } from "antd";

const { Text } = Typography;

export default function KhoHang_Delete({ data, onSuccess, onCancel }) {
  const handleDelete = async () => {
    try {
      await deleteWarehouse(data.ma_kho);
      message.success("Đã xóa kho thành công!");
      onSuccess();
    } catch {
      message.error("Lỗi khi xóa kho!");
    }
  };

  if (!data) return null;
  return (
    <div style={{ padding: 24 }}>
      <Text><PERSON><PERSON>n chắc chắn muốn xoá kho <strong>{data.ten_kho}</strong>?</Text>
      <div style={{ marginTop: 16 }}>
        <Space>
          <Button danger type="primary" onClick={handleDelete}>Xoá</Button>
          <Button onClick={onCancel}>Huỷ</Button>
        </Space>
      </div>
    </div>
  );
}
