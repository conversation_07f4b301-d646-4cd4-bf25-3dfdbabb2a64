import React from "react";
import { deleteWarehouse } from "./khoHang<PERSON><PERSON>";
import { Button, Typography, Space, message } from "antd";

export default function KhoHang_Delete({ data, onSuccess, onCancel }) {
  const handleDelete = async () => {
    try {
      await deleteWarehouse(data.ma_kho);
      message.success("Đã xóa kho thành công!");
      onSuccess();
    } catch {
      message.error("Lỗi khi xóa kho!");
    }
  };

  if (!data) return null;
  return (
    <div style={{ padding: 24 }}>
      <Typography.Text>
        Bạn chắc chắn muốn xoá kho <strong>{data.ten_kho}</strong>?
      </Typography.Text>
        <Button danger type="primary" onClick={handleDelete}>
          Xoá
        </Button>
        <Button onClick={onCancel} style={{ marginLeft: 8 }}>
          Huỷ
        </Button>
      </div>
    </div>
  );
}
