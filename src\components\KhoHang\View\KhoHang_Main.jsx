import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Row,
  Col,
  Button,
  Input,
  Space,
  Tag,
  Divider,
  Modal,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from "@ant-design/icons";
import KhoHang_Add from "../Function/KhoHang_Add";
import KhoHang_Update from "../Function/KhoHang_Update";
import KhoHang_Delete from "../Function/KhoHang_Delete";
import KhoHang_Filter from "../Function/KhoHang_Filter";
import { getWarehouses, getAccountList } from "../Function/khoHangApi";
import dayjs from "dayjs/esm/index.js";
import { Select } from "antd";

export default function KhoHang_Main() {
  const [list, setList] = useState([]);
  const [search, setSearch] = useState("");
  const [openAdd, setOpenAdd] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [selected, setSelected] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [managerFilter, setManagerFilter] = useState("");

  useEffect(() => {
    fetchWarehouses();
    fetchAccounts();
  }, []);

  const fetchWarehouses = async () => {
  const res = await getWarehouses();
  let data = res.data?.data;
  if (!Array.isArray(data)) {
    // Nếu trả về 1 object đơn lẻ, đưa vào mảng
    if (data) data = [data];
    else data = [];
  }
  setList(data);
};

const fetchAccounts = async () => {
  const res = await getAccountList();
  setAccounts(res.data?.data || []);
};

  const handleFilter = (value) => setSearch(value);

  const handleEdit = (kho) => {
    setSelected(kho);
    setOpenUpdate(true);
  };

  const handleDelete = (kho) => {
    setSelected(kho);
    setOpenDelete(true);
  };

  const filteredList = list.filter((kho) =>
    kho.ten_kho.toLowerCase().includes(search.toLowerCase())
  ).filter(kho => {
    if (statusFilter === "") return true;
    return kho.tinh_trang === statusFilter;
  }).filter(kho => {
    if (managerFilter === "") return true;
    return kho.accounts_warehouse_quan_ly_khoToaccounts?.ho_va_ten === managerFilter;
  });

  return (
    <div style={{ padding: '24px' }}>
      <Typography.Title level={2} style={{ color: 'black', marginBottom: '24px' }}>
        🚧 Danh sách Kho đang được migrate ({filteredList.length})
      </Typography.Title>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Typography.Title level={3}>Kho Hàng Module</Typography.Title>
          <Typography.Paragraph>
            Module này đang được migrate từ MUI sang Ant Design.
          </Typography.Paragraph>
          <Typography.Paragraph>
            Hiện tại có {list.length} kho trong hệ thống.
          </Typography.Paragraph>
        </div>
      </Card>
    </div>
  );
}


