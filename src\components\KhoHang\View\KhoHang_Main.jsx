import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Row,
  Col,
  Button,
  Input,
  Space,
  Tag,
  Divider,
  Modal,
  Select,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from "@ant-design/icons";
import KhoHang_Add from "../Function/KhoHang_Add";
import KhoHang_Update from "../Function/KhoHang_Update";
import KhoHang_Delete from "../Function/KhoHang_Delete";
import KhoHang_Filter from "../Function/KhoHang_Filter";
import { getWarehouses, getAccountList } from "../Function/khoHangApi";
import dayjs from "dayjs/esm/index.js";

const { Title, Text } = Typography;
const { Option } = Select;

export default function KhoHang_Main() {
  const [list, setList] = useState([]);
  const [search, setSearch] = useState("");
  const [openAdd, setOpenAdd] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [selected, setSelected] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [managerFilter, setManagerFilter] = useState("");

  useEffect(() => {
    fetchWarehouses();
    fetchAccounts();
  }, []);

  const fetchWarehouses = async () => {
  const res = await getWarehouses();
  let data = res.data?.data;
  if (!Array.isArray(data)) {
    // Nếu trả về 1 object đơn lẻ, đưa vào mảng
    if (data) data = [data];
    else data = [];
  }
  setList(data);
};

const fetchAccounts = async () => {
  const res = await getAccountList();
  setAccounts(res.data?.data || []);
};

  const handleFilter = (value) => setSearch(value);

  const handleEdit = (kho) => {
    setSelected(kho);
    setOpenUpdate(true);
  };

  const handleDelete = (kho) => {
    setSelected(kho);
    setOpenDelete(true);
  };

  const filteredList = list.filter((kho) =>
    kho.ten_kho.toLowerCase().includes(search.toLowerCase())
  ).filter(kho => {
    if (statusFilter === "") return true;
    return kho.tinh_trang === statusFilter;
  }).filter(kho => {
    if (managerFilter === "") return true;
    return kho.accounts_warehouse_quan_ly_khoToaccounts?.ho_va_ten === managerFilter;
  });

  return (
    <div style={{ padding: '16px 24px' }}>
      <Title level={3} style={{ fontWeight: 700, marginBottom: 24, color: 'black' }}>
        Danh sách Kho ({filteredList.length})
      </Title>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Input
            placeholder="Tìm kiếm tên kho"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </Col>
        <Col xs={24} sm={8}>
          <Select
            placeholder="Người quản lý"
            style={{ width: '100%' }}
            value={managerFilter || undefined}
            onChange={(value) => setManagerFilter(value || "")}
            allowClear
          >
            {accounts.map(acc => (
              <Option key={acc.ma_nguoi_dung} value={acc.ho_va_ten}>
                {acc.ho_va_ten}
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={8}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setOpenAdd(true)}
            style={{ width: '100%', fontWeight: 600 }}
          >
            Thêm Kho
          </Button>
        </Col>
      </Row>

      <Space style={{ marginBottom: 16 }}>
        <Tag.CheckableTag
          checked={statusFilter === ""}
          onChange={() => setStatusFilter("")}
        >
          Tất cả trạng thái
        </Tag.CheckableTag>
        <Tag.CheckableTag
          checked={statusFilter === "Đang hoạt động"}
          onChange={() => setStatusFilter("Đang hoạt động")}
        >
          Đang hoạt động
        </Tag.CheckableTag>
        <Tag.CheckableTag
          checked={statusFilter === "Bảo trì"}
          onChange={() => setStatusFilter("Bảo trì")}
        >
          Bảo trì
        </Tag.CheckableTag>
      </Space>

      <Row gutter={[24, 24]}>
        {filteredList.map((kho) => (
          <Col xs={24} md={12} key={kho.ma_kho}>
            <Card
              style={{ borderRadius: 12 }}
              bodyStyle={{ padding: 16 }}
            >
              <Row gutter={16}>
                {/* Hình ảnh kho */}
                <Col span={6}>
                  <img
                    src={kho.hinh_anh || "/warehouse-default.png"}
                    alt="Hình kho"
                    style={{ 
                      width: '100%', 
                      height: 100, 
                      objectFit: "cover", 
                      borderRadius: 8, 
                      border: "1px solid #ccc" 
                    }}
                  />
                </Col>
                
                {/* Thông tin kho */}
                <Col span={12}>
                  <Title level={5} style={{ marginBottom: 8, fontWeight: 600 }}>
                    {kho.ten_kho}
                  </Title>
                  <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
                    {kho.vi_tri_kho}
                  </Text>
                  <div style={{ marginBottom: 8 }}>
                    <Tag color={kho.tinh_trang === "Đang hoạt động" ? "green" : "orange"}>
                      {kho.tinh_trang}
                    </Tag>
                  </div>
                  <div>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Người tạo:</strong> {kho.accounts_warehouse_nguoi_taoToaccounts?.ho_va_ten || kho.nguoi_tao}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Ngày tạo:</strong>{" "}
                      {kho.ngay_tao && dayjs(kho.ngay_tao).format("DD/MM/YYYY")}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Quản lý:</strong> {kho.accounts_warehouse_quan_ly_khoToaccounts?.ho_va_ten || kho.quan_ly_kho}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Ngày kiểm kê gần nhất:</strong>{" "}
                      {kho.ngay_kiem_ke_gan_nhat && dayjs(kho.ngay_kiem_ke_gan_nhat).format("DD/MM/YYYY")}
                    </Text>
                  </div>
                </Col>
                
                {/* Thông tin tài chính và nút */}
                <Col span={6} style={{ textAlign: 'right' }}>
                  <div style={{ marginBottom: 4 }}>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Nhập:</strong> {kho.tong_gia_tri_nhap?.toLocaleString()} VNĐ
                    </Text>
                  </div>
                  <div style={{ marginBottom: 4 }}>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Xuất:</strong> {kho.tong_gia_tri_xuat?.toLocaleString()} VNĐ
                    </Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text style={{ fontSize: 12 }}>
                      <strong>Tồn:</strong> {kho.tong_gia_tri_ton_kho?.toLocaleString()} VNĐ
                    </Text>
                  </div>
                  <Button
                    size="small"
                    type="default"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(kho)}
                  >
                    Sửa
                  </Button>
                </Col>
              </Row>
              
              {kho.ghi_chu && (
                <>
                  <Divider style={{ margin: '8px 0' }} />
                  <Text style={{ fontSize: 12 }}>
                    <strong>Ghi chú:</strong> {kho.ghi_chu}
                  </Text>
                </>
              )}
            </Card>
          </Col>
        ))}
      </Row>

      {/* Modals */}
      <Modal
        title="Thêm Kho Mới"
        open={openAdd}
        onCancel={() => setOpenAdd(false)}
        footer={null}
        width={600}
      >
        <KhoHang_Add
          accounts={accounts}
          onSuccess={() => {
            setOpenAdd(false);
            fetchWarehouses();
          }}
          onCancel={() => setOpenAdd(false)}
        />
      </Modal>
      
      <Modal
        title="Cập Nhật Kho"
        open={openUpdate}
        onCancel={() => setOpenUpdate(false)}
        footer={null}
        width={600}
      >
        <KhoHang_Update
          data={selected}
          accounts={accounts}
          onSuccess={() => {
            setOpenUpdate(false);
            fetchWarehouses();
          }}
          onCancel={() => setOpenUpdate(false)}
        />
      </Modal>
      
      <Modal
        title="Xóa Kho"
        open={openDelete}
        onCancel={() => setOpenDelete(false)}
        footer={null}
      >
        <KhoHang_Delete
          data={selected}
          onSuccess={() => {
            setOpenDelete(false);
            fetchWarehouses();
          }}
          onCancel={() => setOpenDelete(false)}
        />
      </Modal>
    </div>
  );
}
