import React from "react";
import { Card, Button, Typography } from "antd";
import { PlusOutlined } from "@ant-design/icons";

// TODO: This component needs to be fully migrated to Ant Design
// Temporarily returning a stub to allow compilation

export default function KhoHang_Main() {
  return (
    <div style={{ padding: 24 }}>
      <Card>
        <div style={{ textAlign: 'center', padding: 40 }}>
          <PlusOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Typography.Title level={2}>🚧 Kho Hàng Management</Typography.Title>
          <Typography.Text>
            Tính năng quản lý kho hàng đang được migrate sang Ant Design!
          </Typography.Text>
          <div style={{ marginTop: 24 }}>
            <Button type="primary" size="large">
              Sẽ sớm hoàn thành
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
