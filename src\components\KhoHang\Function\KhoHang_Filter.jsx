import React, { useState } from "react";
import { Input, Button, Space } from "antd";
import { SearchOutlined } from "@ant-design/icons";

export default function KhoHang_Filter({ onSearch }) {
  const [text, setText] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch && onSearch(text);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Space>
        <Input
          placeholder="Tìm kiếm tên kho"
          value={text}
          onChange={(e) => setText(e.target.value)}
          style={{ width: 220 }}
          prefix={<SearchOutlined />}
        />
        <Button type="default" htmlType="submit">Tìm</Button>
      </Space>
    </form>
  );
}
