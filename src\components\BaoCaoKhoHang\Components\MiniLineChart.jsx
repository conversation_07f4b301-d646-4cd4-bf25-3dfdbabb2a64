﻿import React from 'react';

const MiniLineChart = ({ data, color = '#1890ff' }) => {
  if (!data || data.length === 0) {
    return <div style={{ width: '100%', height: 32, backgroundColor: '#f5f5f5', borderRadius: 4 }} />;
  }

  // Tìm min/max để normalize dữ liệu
  const values = data.map(d => d.value || 0);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const range = maxValue - minValue;

  // Nếu không có biến động, hiển thị đường thẳng đơn giản ở giữa
  if (range === 0) {
    return (
      <div style={{ width: '100%', height: 32, position: 'relative' }}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          style={{ position: 'absolute', top: 0, left: 0 }}
        >
          {/* Đường thẳng ngang ở giữa */}
          <line
            x1="0"
            y1="50"
            x2="100"
            y2="50"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
          />
        </svg>
      </div>
    );
  }

  // Tạo path SVG cho line chart
  const points = values.map((value, index) => {
    const x = (index / Math.max(1, values.length - 1)) * 100; // % width, tránh chia cho 0
    const y = 100 - ((value - minValue) / range) * 80; // % height (đảo ngược và để margin 20%)
    return `${x},${y}`;
  }).join(' ');

  // Tạo gradient area dưới đường line
  const areaPoints = `0,100 ${points} 100,100`;

  return (
    <div style={{ width: '100%', height: 32, position: 'relative' }}>
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{ position: 'absolute', top: 0, left: 0 }}
      >
        {/* Gradient definition */}
        <defs>
          <linearGradient id={`gradient-${color.replace('#', '')}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.3" />
            <stop offset="100%" stopColor={color} stopOpacity="0.05" />
          </linearGradient>
        </defs>
        
        {/* Area dưới đường line */}
        <polygon
          points={areaPoints}
          fill={`url(#gradient-${color.replace('#', '')})`}
        />
        
        {/* Đường line */}
        <polyline
          points={points}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Điểm cuối (current value) */}
        {points.split(' ').length > 0 && (
          <circle
            cx={points.split(' ').slice(-1)[0].split(',')[0]}
            cy={points.split(' ').slice(-1)[0].split(',')[1]}
            r="2"
            fill={color}
            stroke="#fff"
            strokeWidth="1"
          />
        )}
      </svg>
    </div>
  );
};

export default MiniLineChart;

