import React from "react";
import { Mo<PERSON>, But<PERSON> } from "antd";

// TODO: This component needs to be fully migrated to Ant Design
// Temporarily returning a stub to allow compilation

function FilterDialog({
  filterDialogOpen,
  handleFilterClose,
  filterCriteria,
  handleFilterChange,
  handleFilterApply,
  handleFilterClear,
  loaiBaoTriOptions,
  moTaOptions,
  ketQuaOptions,
  nguyenNhanHuHongOptions,
  isMobile,
}) {
  return (
    <Modal 
      open={filterDialogOpen} 
      onCancel={handleFilterClose} 
      title="🚧 Filter Options - Đang migrate"
      footer={[
        <Button key="clear" onClick={handleFilterClear}>
          Clear
        </Button>,
        <Button key="cancel" onClick={handleFilterClose}>
          Cancel
        </Button>,
        <Button key="apply" type="primary" onClick={handleFilterApply}>
          Apply
        </Button>
      ]}
    >
      <div style={{ padding: 20, textAlign: 'center' }}>
        <p>T<PERSON><PERSON> năng filter sẽ sớm được cập nhật với Ant Design!</p>
      </div>
    </Modal>
  );
}

export default FilterDialog;
