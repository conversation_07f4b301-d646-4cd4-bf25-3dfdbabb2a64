import React from "react";
import {
  Modal,
  Form,
  Select,
  Checkbox,
  Button,
  Input,
  Typography,
} from "antd";

function FilterDialog({
  filterDialogOpen,
  handleFilterClose,
  filterCriteria,
  handleFilterChange,
  handleFilterApply,
  handleFilterClear,
  loaiBaoTriOptions,
  moTaOptions,
  ketQuaOptions,
  nguyenNhanHuHongOptions, // New prop for nguyen_nhan_hu_hong options
  isMobile,
}) {
  return (
    <Modal
      open={filterDialogOpen}
      onCancel={handleFilterClose}
      title="🚧 Filter Options"
      width={600}
      footer={[
        <Button key="clear" onClick={handleFilterClear}>
          Clear
        </Button>,
        <Button key="cancel" onClick={handleFilterClose}>
          Cancel
        </Button>,
        <Button key="apply" type="primary" onClick={handleFilterApply}>
          Apply
        </Button>
      ]}
    >
      <div style={{ textAlign: 'center', padding: 40 }}>
        <Typography.Title level={3}>Filter Dialog</Typography.Title>
        <Typography.Paragraph>
          🚧 Filter dialog đang được migrate sang Ant Design!
        </Typography.Paragraph>
        <Typography.Paragraph>
          Hiện tại có {loaiBaoTriOptions?.length || 0} loại bảo trì options.
        </Typography.Paragraph>
      </div>
    </Modal>
  );
}

export default FilterDialog;

