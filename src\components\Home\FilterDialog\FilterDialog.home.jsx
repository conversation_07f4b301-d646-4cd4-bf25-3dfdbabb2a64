import React from "react";
import { Modal, Row, Col, Select, Button, DatePicker, Space } from "antd";
import dayjs from 'dayjs';

function FilterDialog({
  filterDialogOpen,
  handleFilterClose,
  filterCriteria,
  handleFilterChange,
  handleFilterApply,
  handleFilterClear,
  loaiBaoTriOptions,
  moTaOptions,
  ketQuaOptions,
  nguyenNhanHuHongOptions, // New prop for nguyen_nhan_hu_hong options
}) {
  return (
    <Modal
      title="Filter Options"
      open={filterDialogOpen}
      onCancel={handleFilterClose}
      width={600}
      footer={[
        <Button key="clear" onClick={handleFilterClear}>
          Xóa bộ lọc
        </Button>,
        <Button key="apply" type="primary" onClick={handleFilterApply}>
          Áp dụng
        </Button>,
      ]}
    >
      <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
        <Col span={24}>
          <Select
            mode="multiple"
            placeholder="Loại Bảo Trì"
            value={
              Array.isArray(filterCriteria.loai_bao_tri)
                ? filterCriteria.loai_bao_tri
                : []
            }
            onChange={(value) => handleFilterChange({
              target: { name: 'loai_bao_tri', value }
            })}
            style={{ width: '100%' }}
            options={loaiBaoTriOptions.map(type => ({ label: type, value: type }))}
          />
        </Col>
        <Col span={24}>
          <Select
            mode="multiple"
            placeholder="Mô Tả Công Việc"
            value={
              Array.isArray(filterCriteria.mo_ta_cong_viec)
                ? filterCriteria.mo_ta_cong_viec
                : []
            }
            onChange={(value) => handleFilterChange({
              target: { name: 'mo_ta_cong_viec', value }
            })}
            style={{ width: '100%' }}
            options={moTaOptions.map(desc => ({ label: desc, value: desc }))}
          />
        </Col>
        <Col span={24}>
          <Select
            mode="multiple"
            placeholder="Nguyên Nhân Hư Hỏng"
            value={
              Array.isArray(filterCriteria.nguyen_nhan_hu_hong)
                ? filterCriteria.nguyen_nhan_hu_hong
                : []
            }
            onChange={(value) => handleFilterChange({
              target: { name: 'nguyen_nhan_hu_hong', value }
            })}
            style={{ width: '100%' }}
            options={nguyenNhanHuHongOptions.map(reason => ({ label: reason, value: reason }))}
          />
        </Col>
        <Col span={24}>
          <Select
            mode="multiple"
            placeholder="Kết Quả Bảo Trì"
            value={
              Array.isArray(filterCriteria.ket_qua) ? filterCriteria.ket_qua : []
            }
            onChange={(value) => handleFilterChange({
              target: { name: 'ket_qua', value }
            })}
            style={{ width: '100%' }}
            options={ketQuaOptions.map(result => ({ label: result, value: result }))}
          />
        </Col>
        <Col span={12}>
          <DatePicker
            placeholder="Ngày Bắt Đầu"
            value={filterCriteria.startDate ? dayjs(filterCriteria.startDate) : null}
            onChange={(date) => handleFilterChange({
              target: { name: 'startDate', value: date ? date.format('YYYY-MM-DD') : '' }
            })}
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={12}>
          <DatePicker
            placeholder="Ngày Hoàn Thành"
            value={filterCriteria.endDate ? dayjs(filterCriteria.endDate) : null}
            onChange={(date) => handleFilterChange({
              target: { name: 'endDate', value: date ? date.format('YYYY-MM-DD') : '' }
            })}
            style={{ width: '100%' }}
          />
        </Col>
      </Row>
    </Modal>
  );
}

export default FilterDialog;