import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card, Typography, Row, Col } from 'antd';

import './AqiBoard.css';
const AqiBoard = ({ city }) => {
    const [aqiData, setAqiData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchAqiData = async () => {
            try {
                const response = await axios.get(`/api/aqi`);
                setAqiData(response.data.data);
                setLoading(false);
            } catch (error) {
                setError(error);
                setLoading(false);
            }
        };

        fetchAqiData();
    }, [city]);

    if (loading) return <div style={{ textAlign: 'center', marginTop: '20px', minHeight: '300px', maxHeight: '400px' }}>Loading...</div>;
    if (error) return <div style={{ textAlign: 'center', marginTop: '20px', minHeight: '300px', maxHeight: '400px' }}>Error: {error.message}</div>;

    return (
        <Row justify="center" style={{ marginTop: '20px', marginBottom: '20px', minHeight: '300px', maxHeight: '400px' }}>
            <Col span={24}>
                <Card style={{ width: '100%', borderRadius: '10px' }}>
                    <Typography.Title level={4}>
                        Air Quality Index
                    </Typography.Title>
                    <Typography.Title level={5}>
                        Ho Chi Minh City
                    </Typography.Title>
                    <Typography.Text style={{ display: 'block', marginTop: '10px' }}>
                        AQI: {aqiData && aqiData.aqi}
                    </Typography.Text>
                    <Typography.Text style={{ display: 'block' }}>
                        Dominant Pollutant: {aqiData && aqiData.dominentpol}
                    </Typography.Text>
                    <div>
                        <Typography.Text style={{ display: 'block' }}>
                            PM 2.5: {aqiData && aqiData.iaqi && aqiData.iaqi.pm25 && aqiData.iaqi.pm25.v}
                        </Typography.Text>
                    </div>
                </Card>
            </Col>
        </Row>
    );
};

export default AqiBoard;