{"name": "tiktok", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^2.4.0", "@ant-design/graphs": "^2.1.0", "@ant-design/icons": "^6.0.0", "@date-io/date-fns": "^3.2.1", "@mui/styles": "^5.14.0", "@mui/x-date-pickers": "^8.6.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "antd": "^5.26.3", "axios": "^1.10.0", "chart.js": "^4.4.3", "core-js": "^3.36.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "docx": "^9.5.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jspdf-font": "^1.0.7", "leaflet": "^1.9.4", "mammoth": "^1.9.0", "moment": "^2.30.1", "pdfjs-dist": "^5.0.375", "pdfmake": "^0.2.20", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-circular-gauge": "^1.1.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-gauge-chart": "^0.5.1", "react-icons": "^4.4.0", "react-leaflet": "^4.2.1", "react-qr-code": "^2.0.15", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "recharts": "^3.0.2", "schema-utils": "^4.3.0", "serve": "^14.2.4", "web-vitals": "^2.1.4", "webpack-dev-server": "^5.2.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prettier-watch": "onchange '**/*.js' -- prettier --write {{changed}}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5", "eslint-webpack-plugin": "^5.0.0", "prettier": "2.6.2"}}