// Nuclear option: Complete error suppression for ResizeObserver
export const nuclearErrorSuppression = () => {
  // Store original functions
  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;
  const originalTrace = console.trace;

  // Helper function to check for ResizeObserver errors
  const isResizeObserverError = (arg) => {
    if (!arg) return false;
    const str = String(arg);
    return str.includes('ResizeObserver') || 
           str.includes('undelivered notifications') ||
           str.includes('handleError') ||
           str.includes('bundle.js:138051') ||
           str.includes('bundle.js:138070') ||
           str.includes('loop completed');
  };

  // Override ALL console methods
  console.error = (...args) => {
    if (args.some(isResizeObserverError)) return;
    originalError.apply(console, args);
  };

  console.warn = (...args) => {
    if (args.some(isResizeObserverError)) return;
    originalWarn.apply(console, args);
  };

  console.log = (...args) => {
    if (args.some(isResizeObserverError)) return;
    originalLog.apply(console, args);
  };

  console.trace = (...args) => {
    if (args.some(isResizeObserverError)) return;
    originalTrace.apply(console, args);
  };

  // Don't override Error constructor - it causes issues
  // Instead, we'll catch errors at the handler level

  // Override all error event handlers
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        if (isResizeObserverError(event.message) || 
            isResizeObserverError(event.error?.message) ||
            isResizeObserverError(event.error?.stack)) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
        return listener.call(this, event);
      };
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };

  // Override window error handlers
  window.onerror = (message, source, lineno, colno, error) => {
    if (isResizeObserverError(message) || isResizeObserverError(error?.message)) {
      return true; // Prevent default
    }
    return false;
  };

  window.addEventListener('unhandledrejection', (event) => {
    if (isResizeObserverError(event.reason?.message) || 
        isResizeObserverError(event.reason)) {
      event.preventDefault();
      return false;
    }
  });

  // Override setTimeout and setInterval
  const originalSetTimeout = window.setTimeout;
  const originalSetInterval = window.setInterval;
  const originalRequestAnimationFrame = window.requestAnimationFrame;

  window.setTimeout = function(callback, delay, ...args) {
    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        if (isResizeObserverError(error.message)) return;
        throw error;
      }
    };
    return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
  };

  window.setInterval = function(callback, delay, ...args) {
    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        if (isResizeObserverError(error.message)) return;
        throw error;
      }
    };
    return originalSetInterval.call(this, wrappedCallback, delay, ...args);
  };

  window.requestAnimationFrame = function(callback) {
    const wrappedCallback = function(timestamp) {
      try {
        return callback(timestamp);
      } catch (error) {
        if (isResizeObserverError(error.message)) return;
        throw error;
      }
    };
    return originalRequestAnimationFrame.call(this, wrappedCallback);
  };

  // Override ResizeObserver completely
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = ResizeObserver;
    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback) {
        const safeCallback = (entries, observer) => {
          try {
            // Use multiple async methods to prevent errors
            Promise.resolve().then(() => {
              window.requestAnimationFrame(() => {
                setTimeout(() => {
                  try {
                    callback(entries, observer);
                  } catch (e) {
                    // Completely ignore
                  }
                }, 0);
              });
            }).catch(() => {
              // Ignore promise errors too
            });
          } catch (e) {
            // Ignore all errors
          }
        };
        super(safeCallback);
      }
    };
  }

  // Override React error boundaries
  if (window.React && window.React.Component) {
    const originalComponentDidCatch = window.React.Component.prototype.componentDidCatch;
    if (originalComponentDidCatch) {
      window.React.Component.prototype.componentDidCatch = function(error, errorInfo) {
        if (isResizeObserverError(error.message)) {
          return; // Don't call original componentDidCatch
        }
        return originalComponentDidCatch.call(this, error, errorInfo);
      };
    }
  }

  // Override webpack's error handling if available
  if (window.__webpack_require__) {
    const originalWebpackError = window.__webpack_require__.oe;
    if (originalWebpackError) {
      window.__webpack_require__.oe = function(error) {
        if (isResizeObserverError(error.message)) {
          return; // Don't call original webpack error handler
        }
        return originalWebpackError.call(this, error);
      };
    }
  }

  // Remove any existing error overlays periodically
  const removeErrorOverlays = () => {
    const selectors = [
      'iframe[title*="React"]',
      'iframe[data-reactroot]',
      'div[data-reactroot]',
      '.react-error-overlay',
      '.webpack-dev-server-error-overlay'
    ];
    
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        if (el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });
    });
  };

  // Run overlay removal periodically
  setInterval(removeErrorOverlays, 1000);
  
  // Also run on DOM changes
  if (window.MutationObserver) {
    const observer = new MutationObserver(removeErrorOverlays);
    observer.observe(document.body, { childList: true, subtree: true });
  }

  console.log('Nuclear error suppression activated');
};
