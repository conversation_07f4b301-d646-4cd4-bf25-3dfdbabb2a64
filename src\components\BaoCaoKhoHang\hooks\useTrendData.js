﻿import { useState, useEffect, useMemo } from 'react';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import { warehouseAPI, handleAPIResponse, handleAPIError } from '../utils/api';

/**
 * Hook useTrendData - Tính toán dữ liệu xu hướng cho dashboard báo cáo kho hàng
 * 
 * SỬ DỤNG CÁC API THỰC TẾ:
 * 1. /warehouse/stock-in - Dữ liệu nhập kho (so_luong_nhap, ngay_nhap_hang, ma_hang)
 * 2. /warehouse/stock-out - Dữ liệu xuất kho (so_luong_xuat, ngay_xuat_hang, ma_hang)  
 * 3. /warehouse/inventory - Dữ liệu tồn kho (ton_hien_tai, muc_ton_toi_thieu, ma_hang)
 * 4. /warehouse/products - <PERSON><PERSON> sách sản phẩm (ma_hang, ten_hang, gia_thuc)
 * 
 * LOGIC TÍNH TOÁN:
 * - Tổng nhập: Tổng so_luong_nhap từ stock-in trong tháng hiện tại
 * - Tổng xuất: Tổng so_luong_xuat từ stock-out trong tháng hiện tại  
 * - Tổng tồn: Tổng ton_hien_tai từ inventory (tồn thực tế hiện tại)
 * - Tồn tháng trước: Tính ngược từ công thức "Tồn hiện tại = Tồn trước + Nhập - Xuất"
 * - Mini chart tồn kho: Tính tồn từng ngày bằng cách lùi từ tồn hiện tại
 * 
 * KHÔNG SỬ DỤNG DỮ LIỆU GIẢ/RANDOM
 */

// Extend dayjs với các plugin cần thiết
dayjs.extend(isBetween);
dayjs.extend(dayOfYear);

export const useTrendData = (currentData) => {
  const [historicalData, setHistoricalData] = useState({
    stockIn: [],
    stockOut: [],
    inventory: [],
    products: [],
    loading: true,
    error: null
  });

  // Fetch dữ liệu lịch sử từ API
  useEffect(() => {
    const fetchHistoricalData = async () => {
      try {
        setHistoricalData(prev => ({ ...prev, loading: true, error: null }));

        // Fetch dữ liệu từ các API endpoint thực tế
        const [stockInRes, stockOutRes, inventoryRes, productsRes] = await Promise.all([
          warehouseAPI.getStockIn(),
          warehouseAPI.getStockOut(), 
          warehouseAPI.getInventory(),
          warehouseAPI.getProducts()
        ]);

        const stockInData = handleAPIResponse(stockInRes);
        const stockOutData = handleAPIResponse(stockOutRes);
        const inventoryData = handleAPIResponse(inventoryRes);
        const productsData = handleAPIResponse(productsRes);

        setHistoricalData({
          stockIn: stockInData,
          stockOut: stockOutData,
          inventory: inventoryData,
          products: productsData,
          loading: false,
          error: null
        });

      } catch (error) {
        const errorMsg = handleAPIError(error);
        setHistoricalData(prev => ({
          ...prev,
          loading: false,
          error: errorMsg
        }));
      }
    };

    fetchHistoricalData();
  }, []);

  // Tính toán trendData dựa trên dữ liệu thực từ API
  const trendData = useMemo(() => {
    if (historicalData.loading || !currentData) {
      return {
        nhap: [],
        nhapChange: 0,
        nhapValue: 0,
        xuat: [],
        xuatChange: 0,
        xuatValue: 0,
        ton: [],
        tonChange: 0,
        tonValue: 0,
        mathang: [],
        hethang: [],
        saphet: [],
        comparisonText: 'tháng trước',
        loading: historicalData.loading,
        error: historicalData.error
      };
    }

    // Hàm tính phần trăm thay đổi
    const calculatePercentChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    // Lấy ngày hiện tại
    const currentDate = dayjs();
    
    // Luôn so sánh theo tháng: tháng hiện tại vs tháng trước
    const currentMonthStart = currentDate.startOf('month');
    const currentMonthEnd = currentDate.endOf('month');
    const previousMonthStart = currentDate.subtract(1, 'month').startOf('month');
    const previousMonthEnd = currentDate.subtract(1, 'month').endOf('month');
    
    const comparisonText = `T${previousMonthStart.format('M')}`;

    // Lọc dữ liệu stock-in theo tháng hiện tại
    const currentStockIn = historicalData.stockIn.filter(item => {
      if (!item.ngay_nhap_hang) return false;
      const itemDate = dayjs(item.ngay_nhap_hang);
      return itemDate.isBetween(currentMonthStart, currentMonthEnd, 'day', '[]');
    });

    // Lọc dữ liệu stock-out theo tháng hiện tại
    const currentStockOut = historicalData.stockOut.filter(item => {
      if (!item.ngay_xuat_hang) return false;
      const itemDate = dayjs(item.ngay_xuat_hang);
      return itemDate.isBetween(currentMonthStart, currentMonthEnd, 'day', '[]');
    });

    // Lọc dữ liệu tháng trước
    const previousStockIn = historicalData.stockIn.filter(item => {
      if (!item.ngay_nhap_hang) return false;
      const itemDate = dayjs(item.ngay_nhap_hang);
      return itemDate.isBetween(previousMonthStart, previousMonthEnd, 'day', '[]');
    });

    const previousStockOut = historicalData.stockOut.filter(item => {
      if (!item.ngay_xuat_hang) return false;
      const itemDate = dayjs(item.ngay_xuat_hang);
      return itemDate.isBetween(previousMonthStart, previousMonthEnd, 'day', '[]');
    });

    // Tính tổng kỳ hiện tại (tính theo giá trị, không chỉ số lượng)
    const calculateValue = (items, qtyField) => {
      return items.reduce((sum, item) => {
        const product = historicalData.products.find(p => p.ma_hang === item.ma_hang);
        const quantity = item[qtyField] || 0;
        const price = product?.gia_thuc || 1; // Đặt giá tối thiểu là 1 để tránh nhân với 0
        return sum + (quantity * price);
      }, 0);
    };

    // Tính theo số lượng thay vì giá trị để tránh chênh lệch quá lớn
    const calculateQuantity = (items, qtyField) => {
      return items.reduce((sum, item) => sum + (item[qtyField] || 0), 0);
    };

    const currentTotalNhap = calculateQuantity(currentStockIn, 'so_luong_nhap');
    const currentTotalXuat = calculateQuantity(currentStockOut, 'so_luong_xuat');
    
    // Tồn kho hiện tại: luôn là trạng thái thực tế hiện tại (không phải theo tháng)
    const currentTotalTon = historicalData.inventory.reduce((sum, item) => {
      return sum + (item.ton_hien_tai || 0);
    }, 0);

    // Tính tổng kỳ trước
    const previousTotalNhap = calculateQuantity(previousStockIn, 'so_luong_nhap');
    const previousTotalXuat = calculateQuantity(previousStockOut, 'so_luong_xuat');
    
    // Tồn cuối tháng trước: tính ngược từ công thức kho hàng
    // Tồn cuối tháng trước = Tồn hiện tại - Nhập tháng này + Xuất tháng này  
    const previousTotalTon = Math.max(0, currentTotalTon - currentTotalNhap + currentTotalXuat);

    // Tính % thay đổi
    const nhapChange = calculatePercentChange(currentTotalNhap, previousTotalNhap);
    const xuatChange = calculatePercentChange(currentTotalXuat, previousTotalXuat);
    const tonChange = calculatePercentChange(currentTotalTon, previousTotalTon);

    // Debug: log để kiểm tra dữ liệu và logic tính toán
    console.log('=== TREND DATA DEBUG ===');
    console.log('Thời gian so sánh:', comparisonText);
    console.log('---');
    console.log('NHẬP TRONG THÁNG:');
    console.log('- Tháng hiện tại:', currentTotalNhap.toLocaleString(), '(từ', currentStockIn.length, 'giao dịch)');
    console.log('- Tháng trước:', previousTotalNhap.toLocaleString(), '(từ', previousStockIn.length, 'giao dịch)');
    console.log('- % thay đổi:', nhapChange.toFixed(1) + '%');
    console.log('---');
    console.log('XUẤT TRONG THÁNG:');
    console.log('- Tháng hiện tại:', currentTotalXuat.toLocaleString(), '(từ', currentStockOut.length, 'giao dịch)');
    console.log('- Tháng trước:', previousTotalXuat.toLocaleString(), '(từ', previousStockOut.length, 'giao dịch)');
    console.log('- % thay đổi:', xuatChange.toFixed(1) + '%');
    console.log('---');
    console.log('TỒN KHO (TRẠNG THÁI HIỆN TẠI):');
    console.log('- Tồn hiện tại:', currentTotalTon.toLocaleString(), '(từ', historicalData.inventory.length, 'sản phẩm)');
    console.log('- Tồn cuối tháng trước (tính toán):', previousTotalTon.toLocaleString());
    console.log('- % thay đổi:', tonChange.toFixed(1) + '%');
    console.log('- Kiểm tra công thức: Tồn hiện tại =', previousTotalTon, '+', currentTotalNhap, '-', currentTotalXuat, '=', (previousTotalTon + currentTotalNhap - currentTotalXuat));
    console.log('========================');

    // Tạo dữ liệu mini chart từ 7 ngày gần nhất (chỉ dùng dữ liệu thực)
    const generateTrendDataFromHistory = (dataArray, dateField, valueField, dataType = 'transaction') => {
      const last7Days = [];
      
      for (let i = 6; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day');
        let totalValue = 0;
        
        if (dataType === 'inventory') {
          // Với tồn kho: tính tồn tại thời điểm cuối ngày
          // Tồn ngày X = Tồn hiện tại - tổng (nhập - xuất) từ ngày X+1 đến hôm nay
          let adjustmentFromFutureDays = 0;
          
          // Tính tổng giao dịch từ ngày hôm sau đến hôm nay
          for (let j = i - 1; j >= 0; j--) {
            const futureDate = dayjs().subtract(j, 'day');
            
            // Nhập trong ngày
            const dayStockIn = historicalData.stockIn.filter(item => {
              if (!item.ngay_nhap_hang) return false;
              return dayjs(item.ngay_nhap_hang).format('YYYY-MM-DD') === futureDate.format('YYYY-MM-DD');
            }).reduce((sum, item) => sum + (item.so_luong_nhap || 0), 0);
            
            // Xuất trong ngày
            const dayStockOut = historicalData.stockOut.filter(item => {
              if (!item.ngay_xuat_hang) return false;
              return dayjs(item.ngay_xuat_hang).format('YYYY-MM-DD') === futureDate.format('YYYY-MM-DD');
            }).reduce((sum, item) => sum + (item.so_luong_xuat || 0), 0);
            
            adjustmentFromFutureDays += (dayStockIn - dayStockOut);
          }
          
          totalValue = Math.max(0, currentTotalTon - adjustmentFromFutureDays);
        } else {
          // Với nhập/xuất: tính tổng giao dịch trong ngày
          const dayData = dataArray.filter(item => {
            if (!item[dateField]) return false;
            const itemDate = dayjs(item[dateField]);
            return itemDate.format('YYYY-MM-DD') === date.format('YYYY-MM-DD');
          });
          
          if (valueField === 'quantity') {
            // Tính theo số lượng
            totalValue = dayData.reduce((sum, item) => {
              const quantity = item.so_luong_nhap || item.so_luong_xuat || 0;
              return sum + quantity;
            }, 0);
          } else {
            // Tính theo field thông thường
            totalValue = dayData.reduce((sum, item) => sum + (item[valueField] || 0), 0);
          }
        }
        
        last7Days.push({ value: Math.round(totalValue) });
      }
      return last7Days;
    };

    return {
      nhap: generateTrendDataFromHistory(historicalData.stockIn, 'ngay_nhap_hang', 'quantity', 'transaction'),
      nhapChange,
      nhapValue: currentTotalNhap, // Tổng nhập tháng
      xuat: generateTrendDataFromHistory(historicalData.stockOut, 'ngay_xuat_hang', 'quantity', 'transaction'),
      xuatChange,
      xuatValue: currentTotalXuat, // Tổng xuất tháng
      ton: generateTrendDataFromHistory([], '', 'ton_hien_tai', 'inventory'),
      tonChange: 0, // Không tính % cho tồn tháng
      tonValue: currentTotalNhap - currentTotalXuat, // Tổng tồn tháng = nhập - xuất
      // Dữ liệu cho các card khác - sử dụng dữ liệu thực từ API
      mathang: Array.from({ length: 7 }, (_, i) => {
        const date = dayjs().subtract(6 - i, 'day');
        // Đếm số sản phẩm có giao dịch trong ngày đó
        const dayProducts = new Set([
          ...historicalData.stockIn.filter(item => 
            item.ngay_nhap_hang && dayjs(item.ngay_nhap_hang).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
          ).map(item => item.ma_hang),
          ...historicalData.stockOut.filter(item => 
            item.ngay_xuat_hang && dayjs(item.ngay_xuat_hang).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
          ).map(item => item.ma_hang)
        ]);
        return { value: dayProducts.size || 0 };
      }),
      hethang: Array.from({ length: 7 }, (_, i) => {
        // Đếm số mặt hàng hết hàng (tồn <= 0)
        const hetHang = historicalData.inventory.filter(item => (item.ton_hien_tai || 0) <= 0).length;
        return { value: hetHang };
      }),
      saphet: Array.from({ length: 7 }, (_, i) => {
        // Đếm số mặt hàng sắp hết (0 < tồn < mức tối thiểu)
        const sapHet = historicalData.inventory.filter(item => {
          const ton = item.ton_hien_tai || 0;
          const mucToiThieu = item.muc_ton_toi_thieu || 10;
          return ton > 0 && ton < mucToiThieu;
        }).length;
        return { value: sapHet };
      }),
      comparisonText,
      // Thêm các giá trị tính toán cho cards khác
      mathangValue: historicalData.products?.length || 0,
      hethangValue: historicalData.inventory?.filter(item => (item.ton_hien_tai || 0) <= 0).length || 0,
      saphetValue: historicalData.inventory?.filter(item => {
        const ton = item.ton_hien_tai || 0;
        const mucToiThieu = item.muc_ton_toi_thieu || 10;
        return ton > 0 && ton < mucToiThieu;
      }).length || 0,
      loading: false,
      error: null
    };
  }, [historicalData, currentData]);

  return trendData;
};

