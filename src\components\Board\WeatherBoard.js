import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card, Typography, Spin, Row, Col } from 'antd';
import { WiDaySunny, WiCloud, WiRain, WiSnow, WiFog, WiDaySunnyOvercast} from 'react-icons/wi';

const WeatherDashboard = () => {
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchWeatherData = async () => {
      try {
        const response = await axios.get('/api/weather');
        setWeatherData(response.data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, []);

  if (loading) return <Spin size="large" />;
  if (error) return <Typography.Text type="danger">{`Error: ${error.message}`}</Typography.Text>;

  const renderWeatherIcon = (condition) => {
    switch (condition) {
      case 'Sunny':
        return <WiDaySunny size={50} color='#FFD700'/>;
      case 'clear sky':
        return <WiCloud size={50} color='#00FFFF'/>;
      case 'Partly cloudy':
      case 'Clouds':
        return <WiCloud size={50} color='#00FFFF'/>;
      case 'Rain':
      case 'Light rain':
      case 'Moderate rain':
      case 'Light rain shower':
        return <WiRain size={50} color='blue'/>;
      case 'Snow':
      case 'Light snow':
        return <WiSnow size={50} color='#FFF8DC'/>;
      case 'Fog':
      case 'Overcast':
        return <WiDaySunnyOvercast size={50} color='yellow' />
      case 'Mist':
        return <WiFog size={50} color='#FFD700'/>;
      default:
        return <WiCloud size={50} color='#00FFFF'/>;
    }
  };

  return (
    <Row justify="center" style={{ marginTop: '20px', marginBottom: '20px', borderRadius: '10px', minHeight: '300px', maxHeight: '400px' }}>
      <Col span={24}>
        <Card style={{ width: '100%', borderRadius: '10px' }}>
          <Typography.Title level={4}>
            Weather Dashboard
          </Typography.Title>
          {weatherData && (
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography.Title level={5}>{weatherData.name}</Typography.Title>
              {renderWeatherIcon(weatherData.weather[0].main)}
              <Typography.Text style={{ display: 'block', marginTop: '8px' }}>
                Temperature: {(weatherData.main.temp / 10).toFixed(1)}°C
              </Typography.Text>
              <Typography.Text style={{ display: 'block' }}>
                Humidity: {weatherData.main.humidity}%
              </Typography.Text>
              <Typography.Text style={{ display: 'block' }}>
                Condition: {weatherData.weather[0].description}
              </Typography.Text>
            </div>
          )}
        </Card>
      </Col>
    </Row>
  );
};

export default WeatherDashboard;
