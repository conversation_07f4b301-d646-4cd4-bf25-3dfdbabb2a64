// Direct webpack bundle patch for ResizeObserver errors
export const patchWebpackBundle = () => {
  // Wait for webpack to load
  setTimeout(() => {
    // Try to find and patch the handleError function in webpack bundle
    if (window.webpackJsonp || window.__webpack_require__) {
      // Method 1: Override the specific handleError function
      const scripts = document.getElementsByTagName('script');
      for (let script of scripts) {
        if (script.src && script.src.includes('bundle.js')) {
          // Found the bundle script
          break;
        }
      }

      // Method 2: Monkey patch the global handleError
      const originalHandleError = window.handleError;
      window.handleError = function(error) {
        // Check if it's a ResizeObserver error
        if (error && (
          error.message?.includes('ResizeObserver') ||
          error.toString().includes('ResizeObserver') ||
          error.toString().includes('undelivered notifications')
        )) {
          // Completely ignore ResizeObserver errors
          return;
        }
        
        // Call original handler for other errors
        if (originalHandleError && typeof originalHandleError === 'function') {
          return originalHandleError.call(this, error);
        }
      };

      // Method 3: Override Error constructor temporarily
      const OriginalError = window.Error;
      window.Error = class extends OriginalError {
        constructor(message) {
          if (typeof message === 'string' && 
              (message.includes('ResizeObserver') || 
               message.includes('undelivered notifications'))) {
            // Create a dummy error that won't be thrown
            super('Suppressed ResizeObserver error');
            this.name = 'SuppressedError';
            this.suppressed = true;
            return this;
          }
          super(message);
        }
      };

      // Method 4: Patch console methods at bundle level
      const patchConsole = () => {
        const methods = ['error', 'warn', 'log'];
        methods.forEach(method => {
          const original = console[method];
          console[method] = function(...args) {
            const message = args[0];
            if (typeof message === 'string' && 
                (message.includes('ResizeObserver') || 
                 message.includes('undelivered notifications') ||
                 message.includes('handleError'))) {
              return; // Suppress
            }
            original.apply(console, args);
          };
        });
      };

      patchConsole();

      // Method 5: Intercept all error events at document level
      document.addEventListener('error', (event) => {
        const message = event.message || event.error?.message || '';
        if (message.includes('ResizeObserver') || 
            message.includes('undelivered notifications')) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
      }, true);

      // Method 6: Override setTimeout and setInterval to catch async errors
      const originalSetTimeout = window.setTimeout;
      const originalSetInterval = window.setInterval;

      window.setTimeout = function(callback, delay, ...args) {
        const wrappedCallback = function() {
          try {
            return callback.apply(this, arguments);
          } catch (error) {
            if (error.message?.includes('ResizeObserver') || 
                error.message?.includes('undelivered notifications')) {
              return; // Suppress ResizeObserver errors
            }
            throw error; // Re-throw other errors
          }
        };
        return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
      };

      window.setInterval = function(callback, delay, ...args) {
        const wrappedCallback = function() {
          try {
            return callback.apply(this, arguments);
          } catch (error) {
            if (error.message?.includes('ResizeObserver') || 
                error.message?.includes('undelivered notifications')) {
              return; // Suppress ResizeObserver errors
            }
            throw error; // Re-throw other errors
          }
        };
        return originalSetInterval.call(this, wrappedCallback, delay, ...args);
      };

    }
  }, 0);

  // Additional immediate patches
  const immediatePatches = () => {
    // Patch requestAnimationFrame
    const originalRAF = window.requestAnimationFrame;
    window.requestAnimationFrame = function(callback) {
      const wrappedCallback = function(timestamp) {
        try {
          return callback(timestamp);
        } catch (error) {
          if (error.message?.includes('ResizeObserver') || 
              error.message?.includes('undelivered notifications')) {
            return; // Suppress ResizeObserver errors
          }
          throw error; // Re-throw other errors
        }
      };
      return originalRAF.call(this, wrappedCallback);
    };

    // Patch MutationObserver as well (sometimes related)
    if (window.MutationObserver) {
      const OriginalMutationObserver = window.MutationObserver;
      window.MutationObserver = class extends OriginalMutationObserver {
        constructor(callback) {
          const wrappedCallback = function(mutations, observer) {
            try {
              return callback(mutations, observer);
            } catch (error) {
              if (error.message?.includes('ResizeObserver') || 
                  error.message?.includes('undelivered notifications')) {
                return; // Suppress ResizeObserver errors
              }
              throw error; // Re-throw other errors
            }
          };
          super(wrappedCallback);
        }
      };
    }
  };

  immediatePatches();
};

// Auto-run when imported
patchWebpackBundle();
