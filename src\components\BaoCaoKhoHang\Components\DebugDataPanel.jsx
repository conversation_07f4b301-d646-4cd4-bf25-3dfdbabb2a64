﻿import React, { useState } from 'react';
import { Card, Button, Collapse, Typography, Divider, Tag, Space } from "antd";
import { InfoCircleOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Title, Text } = Typography;

/**
 * Component debug để hiển thị thông tin chi tiết về dữ liệu API
 * Giúp kiểm tra tính nhất quán của dữ liệu nhập-xuất-tồn
 */
const DebugDataPanel = ({ trendData, historicalData, bangNhapXuatTon = [] }) => {
  const [isVisible, setIsVisible] = useState(false);

  if (!trendData || trendData.loading) {
    return null;
  }

  const formatNumber = (num) => {
    return typeof num === 'number' ? num.toLocaleString() : '0';
  };

  const getChangeColor = (change) => {
    if (change > 0) return 'green';
    if (change < 0) return 'red';
    return 'default';
  };

  const calculateInventoryConsistency = () => {
    if (!historicalData) return null;
    
    // Tồn tháng này = Nhập tháng này - Xuất tháng này
    const nhapThang = trendData.nhapValue || 0;
    const xuatThang = trendData.xuatValue || 0;
    const tonThangNay = nhapThang - xuatThang;
    const tonHienTai = trendData.tonValue || 0;
    
    return {
      tonHienTai,
      nhapThang,
      xuatThang,
      tonThangNay,
      isConsistent: true // Luôn nhất quán với công thức đơn giản này
    };
  };

  const consistency = calculateInventoryConsistency();

  return (
    <div style={{ margin: '16px 0' }}>
      <Button 
        type="default" 
        icon={isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
        onClick={() => setIsVisible(!isVisible)}
        style={{ marginBottom: 16 }}
      >
        {isVisible ? 'Ẩn' : 'Hiện'} thông tin chi tiết Card & Công thức
      </Button>

      {isVisible && (
        <Card title={<><InfoCircleOutlined /> Debug Panel - Chi tiết Card và Công thức Nghiệp vụ</>} size="small">
          <Collapse size="small">
            <Panel header="� Debug: Kiểm tra dữ liệu Card vs API" key="debug-data">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Title level={5}>So sánh giá trị từ API vs Fallback:</Title>
                
                <div style={{ background: '#f6ffed', padding: 12, borderRadius: 6 }}>
                  <Text strong>📥 Tổng nhập tháng:</Text>
                  <br />
                  <Text>API (trendData.nhapValue): </Text>
                  <Text code>{trendData.nhapValue !== undefined ? formatNumber(trendData.nhapValue) : 'undefined'}</Text>
                  <br />
                  <Text>Fallback (bangNhapXuatTon): </Text>
                  <Text code>{formatNumber(bangNhapXuatTon?.reduce((sum, r) => sum + r.tong_nhap, 0) || 0)}</Text>
                  <br />
                  <Text type="success">✓ Hiển thị trên Card: {formatNumber(trendData.nhapValue !== undefined ? trendData.nhapValue : (bangNhapXuatTon?.reduce((sum, r) => sum + r.tong_nhap, 0) || 0))}</Text>
                </div>
                
                <div style={{ background: '#fff2f0', padding: 12, borderRadius: 6 }}>
                  <Text strong>📤 Tổng xuất tháng:</Text>
                  <br />
                  <Text>API (trendData.xuatValue): </Text>
                  <Text code>{trendData.xuatValue !== undefined ? formatNumber(trendData.xuatValue) : 'undefined'}</Text>
                  <br />
                  <Text>Fallback (bangNhapXuatTon): </Text>
                  <Text code>{formatNumber(bangNhapXuatTon?.reduce((sum, r) => sum + r.tong_xuat, 0) || 0)}</Text>
                  <br />
                  <Text type={trendData.xuatValue !== undefined ? 'success' : 'warning'}>
                    {trendData.xuatValue !== undefined ? '✓' : '⚠️'} Hiển thị trên Card: {formatNumber(trendData.xuatValue !== undefined ? trendData.xuatValue : (bangNhapXuatTon?.reduce((sum, r) => sum + r.tong_xuat, 0) || 0))}
                  </Text>
                  {trendData.xuatValue === undefined && (
                    <div>
                      <Text type="danger">⚠️ Đang sử dụng dữ liệu fallback vì API chưa trả về xuatValue!</Text>
                    </div>
                  )}
                </div>
                
                <div style={{ background: '#f0f2f5', padding: 12, borderRadius: 6 }}>
                  <Text strong>🔍 Lý do xuất = 984 thay vì 0:</Text>
                  <br />
                  <Text type="secondary">
                    Trước khi sửa: Logic <Text code>xuatValue || fallback</Text> coi 0 là falsy
                    <br />
                    Sau khi sửa: Logic <Text code>xuatValue !== undefined ? xuatValue : fallback</Text> chỉ fallback khi thực sự undefined
                  </Text>
                </div>
              </Space>
            </Panel>

            <Panel header="�📊 Tổng quan số liệu Card" key="overview">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Title level={5}>Các Card hiển thị trên Dashboard:</Title>
                
                <div>
                  <Tag >Card 1: Tổng nhập tháng</Tag>
                  <Text strong>{formatNumber(trendData.nhapValue)}</Text>
                  <Tag color={getChangeColor(trendData.nhapChange)} style={{ marginLeft: 8 }}>
                    {trendData.nhapChange > 0 ? '+' : ''}{trendData.nhapChange?.toFixed(1)}%
                  </Tag>
                  <Text type="secondary"> so với {trendData.comparisonText}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    ✅ Nhập tăng = xu hướng tốt (xanh), Nhập giảm = xu hướng xấu (đỏ)
                  </Text>
                </div>
                
                <div>
                  <Tag >Card 2: Tổng xuất tháng</Tag>
                  <Text strong>{formatNumber(trendData.xuatValue)}</Text>
                  <Tag color={getChangeColor(trendData.xuatChange)} style={{ marginLeft: 8 }}>
                    {trendData.xuatChange > 0 ? '+' : ''}{trendData.xuatChange?.toFixed(1)}%
                  </Tag>
                  <Text type="secondary"> so với {trendData.comparisonText}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    ⚠️ Xuất tăng = xu hướng xấu (đỏ), Xuất giảm = xu hướng tốt (xanh)
                  </Text>
                </div>
                
                <div>
                  <Tag >Card 3: Tổng tồn tháng</Tag>
                  <Text strong>{formatNumber(consistency?.tonThangNay)}</Text>
                  <Text type="secondary"> (Nhập - Xuất trong tháng = biến động tồn kho)</Text>
                </div>
                
                <div>
                  <Tag >Card 4: Tổng mặt hàng</Tag>
                  <Text strong>{formatNumber(trendData.mathangValue)}</Text>
                </div>
                
                <div>
                  <Tag >Card 5: SL hết hàng</Tag>
                  <Text strong>{formatNumber(trendData.hethangValue)}</Text>
                </div>
                
                <div>
                  <Tag >Card 6: SL sắp hết</Tag>
                  <Text strong>{formatNumber(trendData.saphetValue)}</Text>
                </div>
              </Space>
            </Panel>

            {consistency && (
              <Panel header="🔍 Kiểm tra tính nhất quán - Công thức kinh doanh" key="consistency">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Title level={5}>📋 Định nghĩa nghiệp vụ kho hàng:</Title>
                  
                  <div style={{ background: '#f0f2f5', padding: 12, borderRadius: 6 }}>
                    <Text strong>• Tổng nhập tháng:</Text> 
                    <Text> Tổng số lượng hàng nhập vào kho trong tháng này</Text>
                    <br />
                    <Text strong>• Tổng xuất tháng:</Text> 
                    <Text> Tổng số lượng hàng xuất khỏi kho trong tháng này</Text>
                    <br />
                    <Text strong>• Tổng tồn tháng:</Text> 
                    <Text> Biến động tồn kho = Nhập - Xuất (trong tháng)</Text>
                    <br />
                    <Text strong>• Tồn kho hiện tại:</Text> 
                    <Text> Số lượng hàng tồn thực tế hiện tại (trạng thái tuyệt đối)</Text>
                  </div>
                  
                  <div style={{ background: '#fff7e6', padding: 12, borderRadius: 6, marginTop: 8 }}>
                    <Text strong>🎨 Logic màu sắc hiển thị:</Text>
                    <br />
                    <Text type="secondary">
                      • <Text type="success">Nhập tăng</Text> = xu hướng tốt (xanh) | <Text type="danger">Nhập giảm</Text> = xu hướng xấu (đỏ)
                      <br />
                      • <Text type="danger">Xuất tăng</Text> = xu hướng xấu (đỏ) | <Text type="success">Xuất giảm</Text> = xu hướng tốt (xanh)
                      <br />
                      • <Text type="success">Tồn tăng</Text> = xu hướng tốt (xanh) | <Text type="danger">Tồn giảm</Text> = xu hướng xấu (đỏ)
                    </Text>
                  </div>
                  
                  <Divider />
                  
                  <Title level={5}>🧮 Công thức tính toán:</Title>
                  
                  <div style={{ background: '#e6f7ff', padding: 12, borderRadius: 6 }}>
                    <Text strong>Tổng tồn tháng = Tổng nhập tháng - Tổng xuất tháng</Text>
                    <br />
                    <Text type="secondary">
                      {formatNumber(consistency.tonThangNay)} = {formatNumber(consistency.nhapThang)} - {formatNumber(consistency.xuatThang)}
                    </Text>
                  </div>
                  
                  <Divider />
                  
                  <div>
                    <Text>📥 Tổng nhập tháng: </Text>
                    <Text strong style={{ color: '#52c41a' }}>{formatNumber(consistency.nhapThang)}</Text>
                  </div>
                  
                  <div>
                    <Text>📤 Tổng xuất tháng: </Text>
                    <Text strong style={{ color: '#ff4d4f' }}>{formatNumber(consistency.xuatThang)}</Text>
                  </div>
                  
                  <div>
                    <Text>📊 Tổng tồn tháng (biến động): </Text>
                    <Text strong style={{ color: '#722ed1' }}>{formatNumber(consistency.tonThangNay)}</Text>
                    <Text type="secondary"> (hiển thị trên Card 3)</Text>
                  </div>
                  
                  <div>
                    <Text>🏪 Tồn kho hiện tại (từ API): </Text>
                    <Text strong style={{ color: '#1890ff' }}>{formatNumber(consistency.tonHienTai)}</Text>
                    <Text type="secondary"> (trạng thái thực tế)</Text>
                  </div>
                  
                  <Divider />
                  
                  <div style={{ background: '#f6ffed', padding: 12, borderRadius: 6 }}>
                    <Text>✅ Tính nhất quán: </Text>
                    <Tag >
                      LOGIC ĐÚNG ✓
                    </Tag>
                    <br />
                    <Text type="secondary">
                      Card 3 hiển thị "Tổng tồn tháng" = biến động tồn kho trong tháng này
                      <br />
                      Không phải tồn kho tuyệt đối mà là sự thay đổi (+/-) do nhập-xuất
                    </Text>
                  </div>
                </Space>
              </Panel>
            )}

            <Panel header="📈 Dữ liệu Mini Chart (7 ngày)" key="chart-data">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Tag >Nhập hàng</Tag>
                  <Text>{trendData.nhap?.map(d => d.value).join(', ')}</Text>
                </div>
                <div>
                  <Tag >Xuất hàng</Tag>
                  <Text>{trendData.xuat?.map(d => d.value).join(', ')}</Text>
                </div>
                <div>
                  <Tag >Tồn kho</Tag>
                  <Text>{trendData.ton?.map(d => d.value).join(', ')}</Text>
                </div>
              </Space>
            </Panel>

            <Panel header="� Thông tin API & Dữ liệu" key="api-info">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Title level={5}>Nguồn dữ liệu cho từng Card:</Title>
                
                <div style={{ background: '#f9f9f9', padding: 12, borderRadius: 6 }}>
                  <Text strong>📥 Card "Tổng nhập tháng":</Text>
                  <br />
                  <Text type="secondary">
                    - API: /stock-in (phiếu nhập kho)
                    <br />
                    - Tính: Tổng số lượng từ tất cả phiếu nhập trong tháng
                    <br />
                    - Đơn vị: Số lượng sản phẩm (không nhân giá)
                  </Text>
                </div>
                
                <div style={{ background: '#f9f9f9', padding: 12, borderRadius: 6 }}>
                  <Text strong>📤 Card "Tổng xuất tháng":</Text>
                  <br />
                  <Text type="secondary">
                    - API: /stock-out (phiếu xuất kho)
                    <br />
                    - Tính: Tổng số lượng từ tất cả phiếu xuất trong tháng
                    <br />
                    - Đơn vị: Số lượng sản phẩm (không nhân giá)
                    <br />
                    - ⚠️ Nếu xuất = 0 nhưng hiển thị số khác: đang dùng dữ liệu fallback
                  </Text>
                </div>
                
                <div style={{ background: '#f9f9f9', padding: 12, borderRadius: 6 }}>
                  <Text strong>📊 Card "Tổng tồn tháng":</Text>
                  <br />
                  <Text type="secondary">
                    - Công thức: Nhập - Xuất (trong tháng)
                    <br />
                    - Ý nghĩa: Biến động tồn kho, không phải tồn tuyệt đối
                    <br />
                    - Giá trị dương: Nhập nhiều hơn xuất (tồn tăng)
                    <br />
                    - Giá trị âm: Xuất nhiều hơn nhập (tồn giảm)
                  </Text>
                </div>
                
                <div style={{ background: '#f9f9f9', padding: 12, borderRadius: 6 }}>
                  <Text strong>🏪 Card 4-5-6:</Text>
                  <br />
                  <Text type="secondary">
                    - API: /inventory (tồn kho hiện tại)
                    <br />
                    - API: /products (danh sách sản phẩm)
                    <br />
                    - Phản ánh trạng thái thực tế, không phải biến động
                  </Text>
                </div>
              </Space>
            </Panel>

            <Panel header="�🏷️ Thông tin khác" key="other-info">
              <Space direction="vertical">
                <Title level={5}>Ý nghĩa của các Card:</Title>
                <div>
                  <Tag >Card 1-2-3</Tag>
                  <Text>Phản ánh hoạt động trong tháng này (biến động)</Text>
                </div>
                <div>
                  <Tag >Card 4-5-6</Tag>
                  <Text>Phản ánh trạng thái hiện tại (tuyệt đối)</Text>
                </div>
                
                <Divider />
                
                <div>
                  <Tag>Tổng mặt hàng</Tag>
                  <Text strong>{formatNumber(trendData.mathangValue)}</Text>
                  <Text type="secondary"> (số loại sản phẩm)</Text>
                </div>
                <div>
                  <Tag >Hết hàng</Tag>
                  <Text strong>{formatNumber(trendData.hethangValue)}</Text>
                  <Text type="secondary"> (tồn = 0)</Text>
                </div>
                <div>
                  <Tag >Sắp hết</Tag>
                  <Text strong>{formatNumber(trendData.saphetValue)}</Text>
                  <Text type="secondary"> (tồn &lt; 10)</Text>
                </div>
              </Space>
            </Panel>
          </Collapse>
        </Card>
      )}
    </div>
  );
};

export default DebugDataPanel;

