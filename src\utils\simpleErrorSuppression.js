// Simple but effective ResizeObserver error suppression
export const simpleErrorSuppression = () => {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  // Helper to check for ResizeObserver errors
  const isResizeObserverError = (message) => {
    if (!message) return false;
    const str = String(message);
    return str.includes('ResizeObserver loop completed with undelivered notifications') ||
           str.includes('ResizeObserver loop limit exceeded') ||
           str.includes('ResizeObserver') ||
           str.includes('undelivered notifications') ||
           str.includes('handleError') ||
           str.includes('bundle.js:138051') ||
           str.includes('bundle.js:138070') ||
           str.includes('bundle.js:138074') ||
           str.includes('bundle.js:138093');
  };

  // Override console.error
  console.error = function(...args) {
    // Check all arguments for ResizeObserver errors
    const hasResizeObserverError = args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      if (arg && arg.toString) return isResizeObserverError(arg.toString());
      return false;
    });

    if (hasResizeObserverError) {
      return; // Completely suppress ResizeObserver errors
    }

    // Call original console.error for other errors
    originalError.apply(console, args);
  };

  // Override console.warn
  console.warn = function(...args) {
    const hasResizeObserverError = args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      if (arg && arg.toString) return isResizeObserverError(arg.toString());
      return false;
    });

    if (hasResizeObserverError) {
      return; // Completely suppress ResizeObserver warnings
    }

    originalWarn.apply(console, args);
  };

  // Global error handler
  window.onerror = function(message, source, lineno, colno, error) {
    if (isResizeObserverError(message) || 
        (error && isResizeObserverError(error.message))) {
      return true; // Prevent default error handling
    }
    return false; // Let other errors through
  };

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    const message = event.reason?.message || event.reason || '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      return false;
    }
  });

  // Error event handler
  window.addEventListener('error', function(event) {
    const message = event.message || event.error?.message || '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  }, true);

  // Safe ResizeObserver wrapper
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = ResizeObserver;
    
    window.ResizeObserver = function(callback) {
      const safeCallback = function(entries, observer) {
        try {
          // Use requestAnimationFrame to avoid loop issues
          window.requestAnimationFrame(() => {
            try {
              callback(entries, observer);
            } catch (error) {
              if (!isResizeObserverError(error.message)) {
                // Only throw non-ResizeObserver errors
                console.error('Non-ResizeObserver error in callback:', error);
              }
            }
          });
        } catch (error) {
          if (!isResizeObserverError(error.message)) {
            console.error('Non-ResizeObserver error in wrapper:', error);
          }
        }
      };

      return new OriginalResizeObserver(safeCallback);
    };

    // Preserve prototype and static methods
    window.ResizeObserver.prototype = OriginalResizeObserver.prototype;
    Object.setPrototypeOf(window.ResizeObserver, OriginalResizeObserver);
  }

  // Remove error overlays
  const removeErrorOverlays = () => {
    try {
      const selectors = [
        'iframe[title*="React Error Overlay"]',
        'iframe[data-reactroot]',
        'div[data-reactroot]',
        '.react-error-overlay',
        '.webpack-dev-server-error-overlay'
      ];
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          try {
            if (el.parentNode) {
              el.parentNode.removeChild(el);
            }
          } catch (e) {
            // Ignore removal errors
          }
        });
      });
    } catch (e) {
      // Ignore any errors in overlay removal
    }
  };

  // Run overlay removal immediately and periodically
  removeErrorOverlays();
  setInterval(removeErrorOverlays, 2000);

  console.log('Simple ResizeObserver error suppression activated');
};
