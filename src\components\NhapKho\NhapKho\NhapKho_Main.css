/* Container chính */
.bang-nhap-kho-container {
    padding: 1rem;
    background-color: #f4f6f8;
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* <PERSON>h<PERSON><PERSON> ch<PERSON><PERSON> chính - deprecated, sử dụng bang-nhap-kho-container thay thế */
.bang-nhap-kho-wrapper {
    padding: 30px;
    background-color: #f4f6f8;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

/* Vùng cuộn ngang cho bảng */
.bang-nhap-kho-scroll-wrapper {
    width: 100%;
    overflow-x: auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    -webkit-overflow-scrolling: touch; /* Smooth scrolling trên iOS */
}

/* Responsive styling cho các filters */
.nhapkho-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 1rem;
}

.nhapkho-filters .ant-input-affix-wrapper:nth-child(1) { width: 320px; }
.nhapkho-filters .ant-select:nth-child(2) { width: 140px; }
.nhapkho-filters .ant-select:nth-child(3) { width: 140px; }
.nhapkho-filters .ant-select:nth-child(4) { width: 120px; }
.nhapkho-filters .ant-select:nth-child(5) { width: 90px; }

/* Custom scrollbar */
.bang-nhap-kho-scroll-wrapper::-webkit-scrollbar {
    height: 8px;
}

.bang-nhap-kho-scroll-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.bang-nhap-kho-scroll-wrapper::-webkit-scrollbar-thumb {
    background: #bfc4c9;
    border-radius: 4px;
}

.bang-nhap-kho-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a0a4a8;
}

/* Make table more responsive */
.custom-ant-table .ant-table-cell {
    white-space: nowrap;
    padding: 8px;
}

/* Highlight rows on hover */
.custom-ant-table .ant-table-tbody > tr:hover > td {
    background-color: #f0f7ff !important;
}

/* Style pagination to be more visible */
.ant-pagination {
    margin: 16px 0;
    text-align: right;
}

/* Style cho fixed columns */
.custom-ant-table .ant-table-cell-fix-left,
.custom-ant-table .ant-table-cell-fix-right {
    background-color: #fff;
}

.custom-ant-table .ant-table-cell-fix-left-last::after,
.custom-ant-table .ant-table-cell-fix-right-first::after {
    border-right: 1px solid #f0f0f0;
}

/* Media queries cho responsive design */
@media (max-width: 1200px) {
    .bang-nhap-kho-container {
        padding: 0.75rem;
    }

    .nhapkho-filters .ant-input-affix-wrapper:nth-child(1) { width: 280px; }
}

@media (max-width: 768px) {
    .bang-nhap-kho-container {
        padding: 0.5rem;
    }

    .nhapkho-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .nhapkho-filters .ant-input-affix-wrapper,
    .nhapkho-filters .ant-select {
        width: 100% !important;
        margin-bottom: 0.5rem;
    }

    .ant-pagination {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .bang-nhap-kho-container {
        padding: 0.25rem;
    }

    /* Giảm font size cho mobile */
    .custom-ant-table .ant-table-cell {
        font-size: 11px;
        padding: 4px;
    }

    .custom-ant-table .ant-table-thead > tr > th {
        font-size: 11px;
        padding: 4px;
    }
}

