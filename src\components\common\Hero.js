import React from 'react';
import { Typography } from 'antd';
import { FaCloud } from 'react-icons/fa';

const Hero = () => (
  <div
    style={{
      backgroundColor: '#f0f2f5',
      padding: '50px 0',
      textAlign: 'center',
      minHeight: '50vh',
      display: 'flex',
      alignItems: 'center',
    }}
  >
    <div style={{ maxWidth: '768px', margin: '0 auto', padding: '0 20px' }}>
      {/* <FaCloud style={{ fontSize: '100px', color: '#1890ff' }} /> */}
      <Typography.Title level={1} style={{ marginBottom: '16px' }}>
      Công Ty TNHH Dịch Vụ Và Thương Mại Hoàng Phúc Thanh
      </Typography.Title>
      <Typography.Title level={3} style={{ fontWeight: 'normal', color: '#666' }}>
        {/* Real-time air quality monitoring for a healthier environment. */}
      </Typography.Title>
    </div>
  </div>
);

export default Hero;
