import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter } from 'react-router-dom';
import { simpleErrorSuppression } from './utils/simpleErrorSuppression';

import './index.css'; // nếu có CSS global
import App from './App';

// Clean and simple approach

// Apply simple but effective error suppression
simpleErrorSuppression();

ReactDOM.render(
  <HashRouter>
    <App />
  </HashRouter>,
  document.getElementById('root')
);
