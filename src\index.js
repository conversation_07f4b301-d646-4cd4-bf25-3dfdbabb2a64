import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter } from 'react-router-dom';
import { suppressResizeObserverErrors } from './utils/errorSuppression';
import './utils/suppressErrorOverlay'; // Import để chạy ngay

import './index.css'; // nếu có CSS global
import App from './App';

// Aggressive ResizeObserver error suppression
const suppressAllErrors = () => {
  // Override all possible error handlers
  const originalError = console.error;
  const originalWarn = console.warn;

  console.error = (...args) => {
    const message = args[0];
    if (typeof message === 'string' &&
        (message.includes('ResizeObserver') ||
         message.includes('undelivered notifications') ||
         message.includes('handleError') ||
         message.includes('bundle.js:138051') ||
         message.includes('bundle.js:138070'))) {
      return; // Completely suppress
    }
    originalError.apply(console, args);
  };

  console.warn = (...args) => {
    const message = args[0];
    if (typeof message === 'string' &&
        (message.includes('ResizeObserver') ||
         message.includes('undelivered notifications'))) {
      return; // Completely suppress
    }
    originalWarn.apply(console, args);
  };

  // Global error handler
  window.onerror = (message, _source, _lineno, _colno, _error) => {
    if (typeof message === 'string' &&
        (message.includes('ResizeObserver') ||
         message.includes('undelivered notifications') ||
         message.includes('handleError'))) {
      return true; // Prevent default error handling
    }
    return false;
  };

  // Unhandled promise rejection
  window.addEventListener('unhandledrejection', (event) => {
    const message = event.reason?.message || '';
    if (message.includes('ResizeObserver') ||
        message.includes('undelivered notifications')) {
      event.preventDefault();
      return false;
    }
  });
};

// Apply all error suppressions
suppressAllErrors();
suppressResizeObserverErrors();

ReactDOM.render(
  <HashRouter>
    <App />
  </HashRouter>,
  document.getElementById('root')
);
