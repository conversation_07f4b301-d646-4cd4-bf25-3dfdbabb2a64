import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter } from 'react-router-dom';
import { suppressResizeObserverErrors } from './utils/errorSuppression';
import { nuclearErrorSuppression } from './utils/nuclearErrorSuppression';
import './utils/suppressErrorOverlay'; // Import để chạy ngay
import './utils/webpackPatch'; // Import webpack patch

import './index.css'; // nếu có CSS global
import App from './App';

// Ultimate ResizeObserver error suppression
const ultimateErrorSuppression = () => {
  // 1. Patch ResizeObserver at the source
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = ResizeObserver;
    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback) {
        const wrappedCallback = (entries, observer) => {
          window.requestAnimationFrame(() => {
            try {
              callback(entries, observer);
            } catch (e) {
              // Completely ignore ResizeObserver errors
            }
          });
        };
        super(wrappedCallback);
      }
    };
  }

  // 2. Patch webpack's handleError function directly
  const originalHandleError = window.handleError;
  if (originalHandleError) {
    window.handleError = function(..._args) {
      // Don't call original handleError for ResizeObserver
      return;
    };
  }

  // 3. Override console methods completely
  const originalError = console.error;

  console.error = (...args) => {
    const message = String(args[0] || '');
    if (message.includes('ResizeObserver') ||
        message.includes('undelivered notifications') ||
        message.includes('handleError') ||
        message.includes('bundle.js:138051') ||
        message.includes('bundle.js:138070')) {
      return; // Completely suppress
    }
    originalError.apply(console, args);
  };

  console.warn = console.log = () => {}; // Suppress all warnings and logs temporarily

  // 4. Override ALL error event handlers
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error') {
      const wrappedListener = function(event) {
        const message = event.message || event.error?.message || '';
        if (message.includes('ResizeObserver') ||
            message.includes('undelivered notifications') ||
            message.includes('handleError')) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          return false;
        }
        return listener.call(this, event);
      };
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };

  // 5. Global error handlers
  window.onerror = () => true; // Suppress ALL errors temporarily
  window.onunhandledrejection = () => true; // Suppress ALL rejections temporarily

  // 6. Patch React error boundary
  const originalReactError = window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__;
  if (originalReactError) {
    window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__ = {
      ...originalReactError,
      reportRuntimeError: () => {}, // Disable error reporting
    };
  }

  // 7. Remove any existing error overlays
  setTimeout(() => {
    const errorOverlays = document.querySelectorAll('iframe[title*="React"], .react-error-overlay, div[data-reactroot]');
    errorOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });
  }, 100);
};

// Apply all error suppressions - Nuclear option
nuclearErrorSuppression();
ultimateErrorSuppression();
suppressResizeObserverErrors();

ReactDOM.render(
  <HashRouter>
    <App />
  </HashRouter>,
  document.getElementById('root')
);
