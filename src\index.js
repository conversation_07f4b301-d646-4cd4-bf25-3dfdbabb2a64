import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter } from 'react-router-dom';
import { suppressResizeObserverErrors } from './utils/errorSuppression';
import './utils/suppressErrorOverlay'; // Import để chạy ngay

import './index.css'; // nếu có CSS global
import App from './App';

// Suppress ResizeObserver errors
suppressResizeObserverErrors();

ReactDOM.render(
  <HashRouter>
    <App />
  </HashRouter>,
  document.getElementById('root')
);
