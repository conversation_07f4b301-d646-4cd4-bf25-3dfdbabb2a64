import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON>hRouter } from 'react-router-dom';
import initResponsiveErrorHandler from './utils/responsiveErrorHandler';

import './index.css'; // nếu có CSS global
import App from './App';

// Initialize responsive error handler
initResponsiveErrorHandler();

ReactDOM.render(
  <HashRouter>
    <App />
  </HashRouter>,
  document.getElementById('root')
);
