import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Input,
  But<PERSON>,
  DatePicker,
  Typography,
} from "antd";
import dayjs from "dayjs";

const { Title } = Typography;
const { TextArea } = Input;

function EditDialog({
  editDialogOpen,
  handleEditClose,
  editFormData,
  setEditFormData,
  handleSave,
  handleInputChange,
  isMobile,
}) {
  const handleFieldChange = (name, value) => {
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const fields = [
    "id_bao_tri:ID Bảo Trì", // Read-only field
    "id_thiet_bi:ID Thiết Bị",
    "id_seri:ID Serial", // Thêm trường id_seri vào form
    "loai_thiet_bi:<PERSON><PERSON><PERSON>",
    "khach_hang:Kh<PERSON>ch <PERSON>",
    "vi_tri_lap_dat:<PERSON><PERSON>",
    "ngay_bat_dau:<PERSON><PERSON><PERSON>:date",
    "ngay_hoan_thanh:<PERSON><PERSON><PERSON>:date",
    "loai_bao_tri:<PERSON><PERSON><PERSON>",
    "nguoi_phu_trach:<PERSON><PERSON>ời <PERSON> Tr<PERSON>ch",
    "mo_ta_cong_viec:Mô Tả Công Việc::3",
    "nguyen_nhan_hu_hong:Nguyên Nhân Hư Hỏng::3",
    "ket_qua:Kết Qu<PERSON>::3",
    "lich_tiep_theo:Lịch Tiếp Theo:date",
    "trang_thai:Trạng Thái",
    "hinh_anh:Hình Ảnh::3",
  ];

  return (
    <Modal
      title={<Title level={4} style={{ margin: 0 }}>Chỉnh sửa thông tin bảo trì</Title>}
      open={editDialogOpen}
      onCancel={handleEditClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleEditClose}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu thay đổi
        </Button>,
      ]}
    >
      <div style={{ padding: '16px 0' }}>
        <Row gutter={[16, 16]}>
          {fields.map((field) => {
            const [name, label, type, rows] = field.split(":");
            const isReadOnly = name === "id_bao_tri";
            const isTextArea = !!rows;
            const isDate = type === "date";
            
            return (
              <Col xs={24} sm={12} key={name}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ display: 'block', marginBottom: 4, fontWeight: 500 }}>
                    {label}
                  </label>
                  {isDate ? (
                    <DatePicker
                      value={editFormData[name] ? dayjs(editFormData[name]) : null}
                      onChange={(date) => handleFieldChange(name, date ? date.format('YYYY-MM-DD') : '')}
                      style={{ width: '100%' }}
                      format="DD/MM/YYYY"
                    />
                  ) : isTextArea ? (
                    <TextArea
                      value={editFormData[name] || ""}
                      onChange={(e) => handleFieldChange(name, e.target.value)}
                      rows={parseInt(rows)}
                      readOnly={isReadOnly}
                    />
                  ) : (
                    <Input
                      value={editFormData[name] || ""}
                      onChange={(e) => handleFieldChange(name, e.target.value)}
                      readOnly={isReadOnly}
                    />
                  )}
                </div>
              </Col>
            );
          })}
        </Row>
      </div>
    </Modal>
  );
}

export default EditDialog;