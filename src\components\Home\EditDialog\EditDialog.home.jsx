import React from "react";
import { Mo<PERSON>, <PERSON>, Col, Input, But<PERSON>, DatePicker, Space } from "antd";
import dayjs from 'dayjs';

function EditDialog({
  editDialogOpen,
  handleEditClose,
  editFormData,
  setEditFormData,
  handleSave,
  handleInputChange,
}) {
  const renderField = (name, label, type, rows) => {
    const value = editFormData[name] || "";
    
    if (type === "date") {
      return (
        <DatePicker
          placeholder={label}
          value={value ? dayjs(value) : null}
          onChange={(date) => handleInputChange({
            target: { name, value: date ? date.format('YYYY-MM-DD') : '' }
          })}
          style={{ width: '100%' }}
        />
      );
    }
    
    if (rows) {
      return (
        <Input.TextArea
          placeholder={label}
          value={value}
          onChange={handleInputChange}
          name={name}
          rows={parseInt(rows)}
        />
      );
    }
    
    return (
      <Input
        placeholder={label}
        value={value}
        onChange={handleInputChange}
        name={name}
        readOnly={name === "id_bao_tri"}
      />
    );
  };

  return (
    <Modal
      title="Chỉnh sửa thông tin bảo trì"
      open={editDialogOpen}
      onCancel={handleEditClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleEditClose}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu thay đổi
        </Button>,
      ]}
    >
      <Row gutter={[16, 16]} style={{ paddingTop: '16px' }}>
        {[
          "id_bao_tri:ID Bảo Trì", // Read-only field
          "id_thiet_bi:ID Thiết Bị",
          "id_seri:ID Serial", // Thêm trường id_seri vào form
          "loai_thiet_bi:Loại Thiết Bị",
          "khach_hang:Khách Hàng",
          "vi_tri_lap_dat:Vị Trí Lắp Đặt",
          "ngay_bat_dau:Ngày Bắt Đầu:date",
          "ngay_hoan_thanh:Ngày Hoàn Thành:date",
          "loai_bao_tri:Loại Bảo Trì",
          "nguoi_phu_trach:Người Phụ Trách",
          "mo_ta_cong_viec:Mô Tả Công Việc::3",
          "nguyen_nhan_hu_hong:Nguyên Nhân Hư Hỏng::3",
          "ket_qua:Kết Quả::3",
          "lich_tiep_theo:Lịch Tiếp Theo:date",
          "trang_thai:Trạng Thái",
          "hinh_anh:Hình Ảnh::3",
        ].map((field) => {
          const [name, label, type, rows] = field.split(":");
          return (
            <Col span={12} key={name}>
              {renderField(name, label, type, rows)}
            </Col>
          );
        })}
      </Row>
    </Modal>
  );
}

export default EditDialog;