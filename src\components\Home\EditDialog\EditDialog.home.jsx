import React from "react";
import {
  Mo<PERSON>,
  Row,
  Col,
  Input,
  Button,
} from "antd";

function EditDialog({
  editDialogOpen,
  handleEditClose,
  editFormData,
  setEditFormData,
  handleSave,
  handleInputChange,
  isMobile,
}) {
  return (
    <Modal
      open={editDialogOpen}
      onCancel={handleEditClose}
      width={800}
      title={
        <span style={{ fontSize: isMobile ? "16px" : "20px" }}>
          Chỉnh sửa thông tin bảo trì
        </span>
      }
      footer={[
        <Button key="cancel" onClick={handleEditClose} size={isMobile ? "small" : "middle"}>
          Hủy
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          size={isMobile ? "small" : "middle"}
        >
          L<PERSON>u thay đổi
        </Button>
      ]}
    >
      <div style={{ padding: "16px 0" }}>
        <Row gutter={[16, 16]}>
          {[
            "id_bao_tri:ID Bảo Trì", // Read-only field
            "id_thiet_bi:ID Thiết Bị",
            "id_seri:ID Serial", // Thêm trường id_seri vào form
            "loai_thiet_bi:Loại Thiết Bị",
            "khach_hang:Khách Hàng",
            "vi_tri_lap_dat:Vị Trí Lắp Đặt",
            "ngay_bat_dau:Ngày Bắt Đầu:date",
            "ngay_hoan_thanh:Ngày Hoàn Thành:date",
            "loai_bao_tri:Loại Bảo Trì",
            "nguoi_phu_trach:Người Phụ Trách",
            "mo_ta_cong_viec:Mô Tả Công Việc::3",
            "nguyen_nhan_hu_hong:Nguyên Nhân Hư Hỏng::3",
            "ket_qua:Kết Quả::3",
            "lich_tiep_theo:Lịch Tiếp Theo:date",
            "trang_thai:Trạng Thái",
            "hinh_anh:Hình Ảnh::3",
          ].map((field) => {
            const [name, label, type, rows] = field.split(":");
            const isTextArea = !!rows;
            const isReadOnly = name === "id_bao_tri";

            return (
              <Col xs={24} sm={12} key={name}>
                {isTextArea ? (
                  <Input.TextArea
                    placeholder={label}
                    value={editFormData[name] || ""}
                    onChange={handleInputChange}
                    rows={rows ? parseInt(rows) : 3}
                    style={{ fontSize: isMobile ? "14px" : "16px" }}
                  />
                ) : (
                  <Input
                    name={name}
                    placeholder={label}
                    type={type || "text"}
                    value={editFormData[name] || ""}
                    onChange={handleInputChange}
                    readOnly={isReadOnly}
                    style={{
                      fontSize: isMobile ? "14px" : "16px",
                      backgroundColor: isReadOnly ? "#f5f5f5" : undefined
                    }}
                  />
                )}
              </Col>
            );
          })}
        </Row>
      </div>
    </Modal>
  );
}

export default EditDialog;