import React from "react";
import {
  Modal,
  Row,
  Col,
  Input,
  Button,
  Form,
} from "antd";

function EditDialog({
  editDialogOpen,
  handleEditClose,
  editFormData,
  setEditFormData,
  handleSave,
  handleInputChange,
  isMobile,
}) {
  return (
    <Modal
      open={editDialogOpen}
      onCancel={handleEditClose}
      title="Chỉnh sửa thông tin bảo trì"
      width={800}
      footer={[
        <Button key="cancel" onClick={handleEditClose}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleEditSave}>
          Lưu
        </Button>
      ]}
    >
      <Form layout="vertical" style={{ padding: '16px 0' }}>
        <Row gutter={16}>
          <Col span={24}>
            <div style={{ textAlign: 'center', padding: 40 }}>
              <h3>🚧 Edit Dialog đang được migrate</h3>
              <p>Form chỉnh sửa sẽ sớm được cập nhật với Ant Design!</p>
              <p>Dữ liệu hiện tại: {editData ? JSON.stringify(editData, null, 2) : 'Không có'}</p>
            </div>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

export default EditDialog;
