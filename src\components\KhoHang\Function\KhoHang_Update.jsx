import React, { useState, useEffect } from "react";
import { update<PERSON><PERSON><PERSON>, getAccountList } from "./khoHang<PERSON>pi";
import {
  Form,
  Button,
  Input,
  Typography,
  Select,
  DatePicker,
  InputNumber,
  message,
} from "antd";
import dayjs from "dayjs";

const { Title } = Typography;
const { Option } = Select;

export default function KhoHang_Update({ data, onSuccess, onCancel }) {
  const [form] = Form.useForm();
  const [user, setUser] = useState({ ho_va_ten: "", ma_nguoi_dung: "" });
  const [accounts, setAccounts] = useState([]);
  const [previewImg, setPreviewImg] = useState(data?.hinh_anh || "");

  useEffect(() => {
    const storedUser = JSON.parse(localStorage.getItem("userData") || "{}");
    setUser(storedUser);

    // Set form values
    if (data) {
      form.setFieldsValue({
        ...data,
        ngay_kiem_ke_gan_nhat: data.ngay_kiem_ke_gan_nhat ? dayjs(data.ngay_kiem_ke_gan_nhat) : null,
      });
    }

    // Lấy danh sách tài khoản (user)
    async function fetchAccounts() {
      try {
        const resAcc = await getAccountList();
        const dataAcc = (resAcc.data && resAcc.data.data) ? resAcc.data.data : [];
        setAccounts(dataAcc);
      } catch (err) {
        setAccounts([]);
      }
    }
    fetchAccounts();
  }, [data, form]);

  const handleSubmit = async (values) => {
    const body = {
      ...values,
      nguoi_tao: data?.nguoi_tao,
      ngay_kiem_ke_gan_nhat: values.ngay_kiem_ke_gan_nhat 
        ? dayjs(values.ngay_kiem_ke_gan_nhat).format("YYYY-MM-DD")
        : undefined,
    };
    try {
      await updateWarehouse(data.ma_kho, body);
      message.success("Cập nhật kho thành công!");
      onSuccess();
    } catch (err) {
      if (err.response) {
        console.log("API Error:", err.response.data);
        message.error("Có lỗi xảy ra khi cập nhật kho!");
      }
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={4}>Sửa kho</Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="ma_kho"
          label="Mã kho"
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          name="ten_kho"
          label="Tên kho"
          rules={[{ required: true, message: 'Tên kho là bắt buộc!' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="vi_tri_kho"
          label="Vị trí kho"
          rules={[{ required: true, message: 'Vị trí kho là bắt buộc!' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="tinh_trang"
          label="Tình trạng"
          rules={[{ required: true, message: 'Tình trạng là bắt buộc!' }]}
        >
          <Select>
            <Option value="Đang hoạt động">Đang hoạt động</Option>
            <Option value="Bảo trì">Bảo trì</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Người tạo"
        >
          <Input 
            value={user.ho_va_ten && user.ma_nguoi_dung === data?.nguoi_tao ? user.ho_va_ten : data?.nguoi_tao} 
            disabled 
          />
        </Form.Item>

        <Form.Item
          name="quan_ly_kho"
          label="Quản lý kho"
          rules={[{ required: true, message: 'Quản lý kho là bắt buộc!' }]}
        >
          <Select
            showSearch
            placeholder="Chọn người quản lý kho"
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {accounts.map(acc => (
              <Option key={acc.ma_nguoi_dung || acc.MaNguoiDung} value={acc.ma_nguoi_dung || acc.MaNguoiDung}>
                {acc.ho_va_ten || acc.TenDayDu}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="ngay_kiem_ke_gan_nhat"
          label="Ngày kiểm kê gần nhất"
        >
          <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_nhap"
          label="Tổng nhập"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_xuat"
          label="Tổng xuất"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_ton_kho"
          label="Tổng tồn kho"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="ghi_chu"
          label="Ghi chú"
        >
          <Input.TextArea rows={3} />
        </Form.Item>

        <Form.Item style={{ marginTop: 16 }}>
          <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
            Lưu
          </Button>
          <Button onClick={onCancel}>
            Hủy
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
