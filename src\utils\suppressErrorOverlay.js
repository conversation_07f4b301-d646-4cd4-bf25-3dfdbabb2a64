// Suppress React Error Overlay for ResizeObserver errors
export const suppressReactErrorOverlay = () => {
  // Only run in development mode
  if (process.env.NODE_ENV === 'development') {
    // Hide the React Error Overlay entirely for ResizeObserver errors
    const originalConsoleError = console.error;
    
    console.error = (...args) => {
      const message = args[0];
      
      // Check if it's a ResizeObserver error
      if (typeof message === 'string' && 
          (message.includes('ResizeObserver loop completed with undelivered notifications') ||
           message.includes('ResizeObserver'))) {
        // Don't call original console.error for ResizeObserver errors
        return;
      }
      
      // Call original console.error for other errors
      originalConsoleError.apply(console, args);
    };

    // Prevent React Error Boundary from displaying ResizeObserver errors
    const originalErrorHandler = window.addEventListener;
    window.addEventListener = function(type, listener, options) {
      if (type === 'error') {
        const wrappedListener = function(event) {
          if (event.error?.message?.includes('ResizeObserver') || 
              event.message?.includes('ResizeObserver')) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            return false;
          }
          return listener.call(this, event);
        };
        return originalErrorHandler.call(this, type, wrappedListener, options);
      }
      return originalErrorHandler.call(this, type, listener, options);
    };

    // Override the reportError function if available
    if (window.reportError) {
      const originalReportError = window.reportError;
      window.reportError = function(error) {
        if (error?.message?.includes('ResizeObserver')) {
          return; // Don't report ResizeObserver errors
        }
        return originalReportError.call(this, error);
      };
    }

    // Suppress React DevTools error reporting
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const originalOnError = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onError;
      if (originalOnError) {
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onError = function(error) {
          if (error?.message?.includes('ResizeObserver')) {
            return; // Don't report ResizeObserver errors to DevTools
          }
          return originalOnError.call(this, error);
        };
      }
    }

    // Add CSS to hide error overlay if it appears
    const style = document.createElement('style');
    style.id = 'suppress-resize-observer-overlay';
    style.textContent = `
      /* Hide React Error Overlay for ResizeObserver errors */
      iframe[title="React Error Overlay"] {
        display: none !important;
      }
      
      /* Hide any error overlay that might show ResizeObserver errors */
      .react-error-overlay {
        display: none !important;
      }
      
      /* Hide webpack dev server error overlay */
      .webpack-dev-server-error-overlay {
        display: none !important;
      }
    `;
    
    // Add the style to head when DOM is ready
    if (document.head) {
      document.head.appendChild(style);
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        document.head.appendChild(style);
      });
    }
  }
};

// Auto-run when imported
suppressReactErrorOverlay();
