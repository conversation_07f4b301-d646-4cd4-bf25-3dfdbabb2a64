import { Button, Space, Tag } from 'antd';

export const getTonKhoColumns = (handleEdit, canEdit) => [
    {
        title: 'STT',
        dataIndex: 'stt',
        key: 'stt',
        width: 60,
        fixed: 'left',
        align: 'center'
    },
    {
        title: 'Mã hàng',
        dataIndex: 'ma_hang',
        key: 'ma_hang',
        width: 120,
        sorter: (a, b) => (a.ma_hang || '').localeCompare(b.ma_hang || ''),
        ellipsis: true
    },
    {
        title: 'Kho',
        dataIndex: ['warehouse', 'ten_kho'],
        key: 'ten_kho',
        width: 150,
        sorter: (a, b) => {
            const aName = a.warehouse?.ten_kho || '';
            const bName = b.warehouse?.ten_kho || '';
            return aName.localeCompare(bName, 'vi');
        },
        ellipsis: true
    },
    {
        title: 'Trạng thái',
        key: 'trang_thai',
        width: 180,
        render: (_, record) => {
            let color = '';
            let text = '';

            if (record.ton_hien_tai > record.muc_ton_toi_thieu * 2.5) {
                color = 'orange';
                text = 'Dư hàng tồn quá nhiều';
            } else if (record.ton_hien_tai < record.muc_ton_toi_thieu) {
                color = 'red';
                text = 'Thiếu hàng';
            } else {
                color = 'green';
                text = 'Hàng ổn định';
            }

            return (
                <Tag
                    color={color}
                    style={{
                        fontSize: '11px',
                        borderRadius: '6px',
                        padding: '2px 6px',
                        fontWeight: 'bold',
                        display: 'inline-block',
                        textAlign: 'center',
                        width: '100%',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                    }}
                >
                    {text}
                </Tag>
            );
        }
    },
    {
        title: 'Tồn trước đó',
        dataIndex: 'ton_truoc_do',
        key: 'ton_truoc_do',
        width: 110,
        align: 'right',
        sorter: (a, b) => (Number(a.ton_truoc_do) || 0) - (Number(b.ton_truoc_do) || 0)
    },
    {
        title: 'Tổng nhập',
        dataIndex: 'tong_nhap',
        key: 'tong_nhap',
        width: 100,
        align: 'right',
        sorter: (a, b) => (Number(a.tong_nhap) || 0) - (Number(b.tong_nhap) || 0)
    },
    {
        title: 'Tổng xuất',
        dataIndex: 'tong_xuat',
        key: 'tong_xuat',
        width: 100,
        align: 'right',
        sorter: (a, b) => (Number(a.tong_xuat) || 0) - (Number(b.tong_xuat) || 0)
    },
    {
        title: 'Tồn hiện tại',
        dataIndex: 'ton_hien_tai',
        key: 'ton_hien_tai',
        width: 110,
        align: 'right',
        sorter: (a, b) => (Number(a.ton_hien_tai) || 0) - (Number(b.ton_hien_tai) || 0)
    },
    {
        title: 'Mức tồn tối thiểu',
        dataIndex: 'muc_ton_toi_thieu',
        key: 'muc_ton_toi_thieu',
        width: 140,
        align: 'right',
        sorter: (a, b) => (Number(a.muc_ton_toi_thieu) || 0) - (Number(b.muc_ton_toi_thieu) || 0)
    },
    {
        title: 'Hành động',
        key: 'hanh_dong',
        width: 100,
        fixed: 'right',
        align: 'center',
        render: (_, record) => (
            <Space>
                <Button
                    type="primary"
                    size="small"
                    disabled={!canEdit}
                    onClick={() => handleEdit(record)}
                    style={{ fontSize: '11px' }}
                >
                    Sửa
                </Button>
            </Space>
        )
    },
];
