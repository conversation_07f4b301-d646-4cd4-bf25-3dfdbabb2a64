import React from "react";
import {
  Button,
  Modal,
  Typography,
  Upload,
  message,
  Space,
  Row,
  Col,
} from "antd";
import { UploadOutlined, ImportOutlined, ExportOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

function ImportDialog({
  importDialogOpen,
  handleImportClose,
  selectedFile,
  setSelectedFile,
  importStatus,
  setImportStatus,
  handleFileChange,
  handleImport,
  fileInputRef,
  filteredData,
  columns,
  isMobile,
  handleImportClick,
  selectedIds,
}) {
  
  // Upload properties for Ant Design
  const uploadProps = {
    accept: '.csv',
    beforeUpload: (file) => {
      handleFileChange({ target: { files: [file] } });
      return false; // Prevent auto upload
    },
    fileList: selectedFile ? [{ 
      uid: '-1', 
      name: selectedFile.name, 
      status: 'done' 
    }] : [],
    onRemove: () => {
      setSelectedFile(null);
    },
    showUploadList: {
      showDownloadIcon: false,
      showPreviewIcon: false,
    }
  };

  const handleExport = () => {
    if (!selectedIds || selectedIds.length === 0) {
      message.warning("<PERSON>ui lòng chọn ít nhất một thiết bị để xuất.");
      return;
    }

    const headers = [
      "STT",
      "ID Bảo Trì", 
      "ID Thiết Bị",
      "ID Số Seri",
      "Loại Thiết Bị",
      "Khách Hàng",
      "Vị Trí Lắp Đặt",
      "Ngày Bắt Đầu",
      "Ngày Hoàn Thành",
      "Loại Bảo Trì",
      "Người Phụ Trách",
      "Mô Tả Công Việc",
      "Nguyên Nhân Hư Hỏng",
      "Kết Quả",
      "Lịch Tiếp Theo",
      "Trạng Thái",
      "Hình Ảnh",
      ...columns,
    ];

    const csvContent = [
      headers.join(","),
      ...filteredData
        .filter((row) => selectedIds.includes(row.id_thiet_bi))
        .map((row) =>
          [
            row.STT || "",
            row.id_bao_tri || "",
            row.id_thiet_bi || "",
            row.id_seri || "",
            row.loai_thiet_bi || "",
            row.khach_hang || "",
            row.vi_tri_lap_dat || "",
            row.ngay_bat_dau || "",
            row.ngay_hoan_thanh || "",
            row.loai_bao_tri || "",
            row.nguoi_phu_trach || "",
            `"${(row.mo_ta_cong_viec || "").replace(/"/g, '""')}"`,
            `"${(row.nguyen_nhan_hu_hong || "").replace(/"/g, '""')}"`,
            `"${(row.ket_qua || "").replace(/"/g, '""')}"`,
            row.lich_tiep_theo || "",
            row.trang_thai || "",
            row.hinh_anh || "",
            ...columns.map((col) => `"${(row[col] || "").replace(/"/g, '""')}"`),
          ].join(",")
        ),
    ].join("\n");

    const link = document.createElement("a");
    link.href = encodeURI("data:text/csv;charset=utf-8," + csvContent);
    link.download = `bao-tri-thiet-bi-${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
    
    message.success("Xuất file thành công!");
  };

  return (
    <>
      {/* Action Buttons Row */}
      <Row 
        justify={isMobile ? "center" : "end"} 
        gutter={[8, 8]} 
        style={{ marginBottom: 16 }}
      >
        <Col>
          <Button
            type="primary"
            icon={<ImportOutlined />}
            onClick={handleImportClick}
            size={isMobile ? "small" : "default"}
          >
            Import
          </Button>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<ExportOutlined />}
            onClick={handleExport}
            size={isMobile ? "small" : "default"}
            style={{ 
              backgroundColor: "#ab47bc", 
              borderColor: "#ab47bc" 
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = "#9c27b0";
              e.target.style.borderColor = "#9c27b0";
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = "#ab47bc";
              e.target.style.borderColor = "#ab47bc";
            }}
          >
            Export
          </Button>
        </Col>
      </Row>

      {/* Import Modal */}
      <Modal
        title="Import CSV File"
        open={importDialogOpen}
        onCancel={handleImportClose}
        width={isMobile ? "90%" : 520}
        footer={[
          <Button 
            key="cancel" 
            onClick={handleImportClose}
            size={isMobile ? "small" : "default"}
          >
            Cancel
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={handleImport}
            loading={importStatus.loading}
            disabled={!selectedFile || importStatus.loading}
            size={isMobile ? "small" : "default"}
          >
            {importStatus.loading ? "Importing..." : "Import"}
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          
          {/* File Upload */}
          <Upload.Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">
              Click hoặc kéo file CSV vào đây để upload
            </p>
            <p className="ant-upload-hint">
              Chỉ hỗ trợ file .csv
            </p>
          </Upload.Dragger>

          {/* Selected File Display */}
          {selectedFile && (
            <div style={{ 
              padding: 12, 
              backgroundColor: '#f6f8fa', 
              borderRadius: 6,
              border: '1px solid #d0d7de'
            }}>
              <Text strong>File đã chọn: </Text>
              <Text code>{selectedFile.name}</Text>
            </div>
          )}

          {/* Status Messages */}
          {importStatus.loading && (
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">Đang import...</Text>
            </div>
          )}

          {importStatus.error && (
            <div style={{ 
              padding: 12, 
              backgroundColor: '#fff2f0', 
              borderRadius: 6,
              border: '1px solid #ffccc7'
            }}>
              <Text type="danger">{importStatus.error}</Text>
            </div>
          )}

          {importStatus.success && (
            <div style={{ 
              padding: 12, 
              backgroundColor: '#f6ffed', 
              borderRadius: 6,
              border: '1px solid #b7eb8f'
            }}>
              <Text type="success">Import hoàn thành thành công!</Text>
            </div>
          )}

        </Space>
      </Modal>
    </>
  );
}

export default ImportDialog;