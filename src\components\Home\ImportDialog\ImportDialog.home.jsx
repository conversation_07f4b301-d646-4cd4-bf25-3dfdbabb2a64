import React from "react";
import { Modal, Button, Typography } from "antd";

// TODO: This component needs to be fully migrated to Ant Design
// Temporarily returning a stub to allow compilation

function ImportDialog({
  importDialogOpen,
  handleImportClose,
  selectedFile,
  setSelectedFile,
  importStatus,
  setImportStatus,
  handleFileChange,
  handleImport,
  fileInputRef,
  filteredData,
  columns,
  isMobile,
  handleImportClick,
  selectedIds,
}) {
  return (
    <Modal 
      open={importDialogOpen} 
      onCancel={handleImportClose} 
      title="🚧 Import Data - Đang migrate"
      footer={[
        <Button key="cancel" onClick={handleImportClose}>
          Cancel
        </Button>,
        <Button key="import" type="primary" onClick={handleImport}>
          Import
        </Button>
      ]}
    >
      <div style={{ padding: 20, textAlign: 'center' }}>
        <Typography.Text>Tính năng import sẽ sớm được cập nhật với Ant Design!</Typography.Text>
      </div>
    </Modal>
  );
}

export default ImportDialog;
