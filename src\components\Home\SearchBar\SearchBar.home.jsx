import React from "react";
import { Input, Button, Space } from "antd";

function SearchBar({ searchId, setSearchId, handleFilterClick, isMobile }) {
  return (
    <Space
      direction={isMobile ? "vertical" : "horizontal"}
      size="middle"
      style={{
        width: "100%",
        marginBottom: 24,
      }}
    >
      <Input
        placeholder="Tìm kiếm theo ID Thiết Bị"
        value={searchId}
        onChange={(e) => setSearchId(e.target.value)}
        style={{
          flex: 1,
          maxWidth: isMobile ? "100%" : 500,
          backgroundColor: "#fff",
          fontSize: isMobile ? "14px" : "16px",
        }}
      />
      <Button
        type="default"
        onClick={handleFilterClick}
        style={{
          minWidth: isMobile ? "100%" : 100,
          fontSize: isMobile ? "12px" : "14px",
        }}
      >
        Filter
      </Button>
    </Space>
  );
}

export default SearchBar;