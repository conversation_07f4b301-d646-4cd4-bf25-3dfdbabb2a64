import React from "react";
import { Input, Button, Space } from "antd";

function SearchBar({ searchId, setSearchId, handleFilterClick }) {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: "16px",
        marginBottom: "24px",
        flexWrap: "wrap",
      }}
    >
      <Input
        placeholder="Tìm kiếm theo ID Thiết Bị"
        value={searchId}
        onChange={(e) => setSearchId(e.target.value)}
        style={{
          flex: 1,
          maxWidth: 500,
          backgroundColor: "#fff",
        }}
      />
      <Button
        type="default"
        onClick={handleFilterClick}
        style={{
          minWidth: 100,
        }}
      >
        Filter
      </Button>
    </div>
  );
}

export default SearchBar;