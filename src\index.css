body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

* {
  box-sizing: border-box;
}
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Suppress React Error Overlay for ResizeObserver errors */
iframe[title*="React Error Overlay"],
iframe[data-reactroot],
div[data-reactroot],
.react-error-overlay,
.webpack-dev-server-error-overlay {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Hide any error overlay that might appear */
body > div[style*="position: fixed"][style*="z-index"] {
  display: none !important;
}

/* Ensure main app is always visible */
#root {
  display: block !important;
  visibility: visible !important;
}
