<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Mainternance</title>

    <!-- Ultimate ResizeObserver Error Suppression -->
    <script>
      // Immediate error suppression before any other scripts load
      (function() {
        'use strict';

        // 1. Override console methods immediately
        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = function(...args) {
          const message = String(args[0] || '');
          if (message.includes('ResizeObserver') ||
              message.includes('undelivered notifications') ||
              message.includes('handleError') ||
              message.includes('bundle.js:138051') ||
              message.includes('bundle.js:138070')) {
            return; // Suppress completely
          }
          originalError.apply(console, args);
        };

        console.warn = function(...args) {
          const message = String(args[0] || '');
          if (message.includes('ResizeObserver') ||
              message.includes('undelivered notifications')) {
            return; // Suppress completely
          }
          originalWarn.apply(console, args);
        };

        // 2. Global error handlers
        window.onerror = function(message, source, lineno, colno, error) {
          if (typeof message === 'string' &&
              (message.includes('ResizeObserver') ||
               message.includes('undelivered notifications') ||
               message.includes('handleError'))) {
            return true; // Prevent default error handling
          }
          return false;
        };

        window.addEventListener('unhandledrejection', function(event) {
          const message = event.reason?.message || '';
          if (message.includes('ResizeObserver') ||
              message.includes('undelivered notifications')) {
            event.preventDefault();
            return false;
          }
        });

        // 3. Patch ResizeObserver immediately
        if (typeof ResizeObserver !== 'undefined') {
          const OriginalResizeObserver = ResizeObserver;
          window.ResizeObserver = class extends OriginalResizeObserver {
            constructor(callback) {
              const wrappedCallback = function(entries, observer) {
                try {
                  window.requestAnimationFrame(() => {
                    try {
                      callback(entries, observer);
                    } catch (e) {
                      // Ignore all ResizeObserver errors
                    }
                  });
                } catch (e) {
                  // Ignore all ResizeObserver errors
                }
              };
              super(wrappedCallback);
            }
          };
        }

        // 4. Prevent React Error Overlay
        const style = document.createElement('style');
        style.textContent = `
          iframe[title*="React Error Overlay"],
          iframe[data-reactroot],
          div[data-reactroot],
          .react-error-overlay,
          .webpack-dev-server-error-overlay {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
          }
        `;
        document.head.appendChild(style);

      })();
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root" style="height: 100vh"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
