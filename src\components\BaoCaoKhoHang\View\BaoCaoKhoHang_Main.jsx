﻿import React, { useEffect } from "react";
import ErrorBoundary from "../../common/ErrorBoundary";
import { useWarehouseData, useWarehouseFilters, useWarehouseCalculations } from "../hooks/useWarehouseData";
import { useTrendData } from "../hooks/useTrendData";
import PageHeader from "../Components/PageHeader";
import FilterControls from "../Components/FilterControls";
import SummaryCards from "../Components/SummaryCards";
import ChartSection from "../Components/ChartSection";
import DataTable from "../Components/DataTable";
import DebugDataPanel from "../Components/DebugDataPanel";
import dayjs from "dayjs";
import isBetween from 'dayjs/plugin/isBetween';

// Extend dayjs với plugin isBetween
dayjs.extend(isBetween);

export default function BaoCaoKhoHang_Main() {
  // Custom hooks for data and state management
  const warehouseData = useWarehouseData();
  const filters = useWarehouseFilters();
  const calculations = useWarehouseCalculations(warehouseData, filters);

  // Dữ liệu đã được filter theo ngày tháng & kho hàng trong hook useWarehouseCalculations
  const filteredCalculations = calculations;

  // Sử dụng hook mới để lấy dữ liệu trend thực tế từ API
  const trendData = useTrendData(filteredCalculations.bangNhapXuatTon);

  // Suppress ResizeObserver error
  useEffect(() => {
    const handleResizeObserverError = (e) => {
      if (e.message && e.message.includes('ResizeObserver loop completed with undelivered notifications')) {
        e.preventDefault();
        e.stopPropagation();
        console.log('ResizeObserver error suppressed');
      }
    };
    
    window.addEventListener('error', handleResizeObserverError);
    
    return () => {
      window.removeEventListener('error', handleResizeObserverError);
    };
  }, []);

  const handleRefresh = () => {
    filters.resetFilters();
  };

  return (
    <ErrorBoundary>
      <div style={{padding: 24, backgroundColor: '#f5f5f5', minHeight: '100vh'}}>
        
        <PageHeader />
        
        <FilterControls
          warehouses={warehouseData.warehouses}
          products={warehouseData.products}
          selectedWarehouse={filters.selectedWarehouse}
          setSelectedWarehouse={filters.setSelectedWarehouse}
          dateRange={filters.dateRange}
          setDateRange={filters.setDateRange}
          selectedProduct={filters.selectedProduct}
          setSelectedProduct={filters.setSelectedProduct}
          onRefresh={handleRefresh}
        />
        
        <SummaryCards 
          bangNhapXuatTon={filteredCalculations.bangNhapXuatTon} 
          trendData={trendData}
        />
        
        {/* Debug Panel để kiểm tra dữ liệu API */}
        <DebugDataPanel 
          trendData={trendData}
          historicalData={warehouseData}
          bangNhapXuatTon={filteredCalculations.bangNhapXuatTon}
        />
        
        <ChartSection 
          tongHopTheoThang={filteredCalculations.tongHopTheoThang}
          pieData={filteredCalculations.pieData}
          bangNhapXuatTon={filteredCalculations.bangNhapXuatTon}
        />
        
        <DataTable 
          bangNhapXuatTon={filteredCalculations.bangNhapXuatTon}
          loading={warehouseData.loading}
        />
        
      </div>
    </ErrorBoundary>
  );
}
