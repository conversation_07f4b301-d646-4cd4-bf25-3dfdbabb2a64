import React, { useState, useEffect } from 'react';
import { Card, Avatar, Typography, Row, Col, Button, Collapse } from 'antd';
import { UserOutlined, ExpandAltOutlined, EllipsisOutlined, HeartOutlined, ShareAltOutlined } from '@ant-design/icons';
// import UserData from '../../database/user.js';
import axios from '../../api/axios';

const { Text, Title, Paragraph } = Typography;
const { Panel } = Collapse;
const { Meta } = Card;

const imgProfile = `${process.env.PUBLIC_URL}image/img_profile.jpg`;

function Profile(props) {
  const [expanded, setExpanded] = useState(false);
  const [user, setUser] = useState();

  useEffect(() => {
    let userId = localStorage.getItem('userId');
    console.log('userId: ', userId);
    axios
      .get(`/api/v1/users/info`)
      .then((res) => {
        let user = res.data.data.info;
        setUser(user);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);
  // console.log('user '+user)

  const handleExpandClick = () => {
    setExpanded(!expanded);
    console.log('user: ', user);
  };

  return user === undefined ? (
    <Title level={3}>User is undefined</Title>
  ) : (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '20px',
        }}
      >
        <Card
          style={{ 
            maxWidth: 900, 
            minWidth: 700,
            width: '100%'
          }}
          cover={
            <img 
              alt="Profile" 
              src={imgProfile} 
              style={{ 
                height: 200, 
                objectFit: 'cover' 
              }} 
            />
          }
          actions={[
            <HeartOutlined key="heart" />,
            <ShareAltOutlined key="share" />,
            <Button 
              key="expand"
              type="text" 
              icon={<ExpandAltOutlined />} 
              onClick={handleExpandClick}
            >
              {expanded ? 'Thu gọn' : 'Xem thêm'}
            </Button>,
          ]}
        >
          <Meta
            avatar={<Avatar size="large" icon={<UserOutlined />} />}
            title={user.fullName}
            description="My monitoring"
            style={{ marginBottom: 16 }}
          />
          
          <div style={{ marginBottom: 16 }}>
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Text><strong>Account ID:</strong> {user._id}</Text>
              </Col>
              <Col span={24}>
                <Text><strong>Email:</strong> {user.email}</Text>
              </Col>
              <Col span={24}>
                <Text><strong>Phone:</strong> {user.phone}</Text>
              </Col>
            </Row>
          </div>

          {expanded && (
            <div style={{ marginTop: 16, borderTop: '1px solid #f0f0f0', paddingTop: 16 }}>
              <Title level={5}>Profile:</Title>
              <Paragraph>This is website for smart house.</Paragraph>
              <Paragraph>Detail profile</Paragraph>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}

export default Profile;
