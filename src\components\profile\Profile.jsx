import React, { useState, useEffect } from 'react';
import { Card, Avatar, Typography, Row } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import axios from '../../api/axios';
// import UserData from '../../database/user.js';
const imgProfile = `${process.env.PUBLIC_URL}image/img_profile.jpg`;

// TODO: This component needs to be fully migrated to Ant Design
// Temporarily returning a simplified version

function Profile() {
  const [user, setUser] = useState();

  useEffect(() => {
    let userId = localStorage.getItem('userId');
    console.log('userId: ', userId);
    axios
      .get(`/api/v1/users/info`)
      .then((res) => {
        let user = res.data.data.info;
        setUser(user);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  return user === undefined ? (
    <Typography.Title level={3}>User is undefined</Typography.Title>
  ) : (
    <Row justify="center" style={{ padding: '20px' }}>
      <Card
        style={{ maxWidth: 900, minWidth: 700 }}
        cover={
          <img
            alt="profile"
            src={imgProfile}
            style={{ height: 200, objectFit: 'cover' }}
          />
        }
        actions={[
          <Typography.Text key="info">🚧 Profile đang được migrate sang Ant Design</Typography.Text>
        ]}
      >
        <Card.Meta
          avatar={<Avatar size="large" icon={<UserOutlined />} />}
          title={user?.name || user?.ho_va_ten || "User Profile"}
          description={
            <div>
              <Typography.Text>Email: {user?.email || "N/A"}</Typography.Text><br/>
              <Typography.Text>Role: {user?.role || "N/A"}</Typography.Text>
            </div>
          }
        />
      </Card>
    </Row>
  );
}

export default Profile;

