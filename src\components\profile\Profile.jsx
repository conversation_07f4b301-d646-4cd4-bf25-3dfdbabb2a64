import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Avatar, 
  Typography, 
  Button, 
  Input, 
  Modal, 
  Row, 
  Col, 
  Space,
  Divider,
  message
} from 'antd';
import { 
  UserOutlined, 
  EditOutlined, 
  ExpandAltOutlined,
  EllipsisOutlined 
} from '@ant-design/icons';
import axios from '../../api/axios';

const { Title, Text } = Typography;
const { Meta } = Card;

const imgProfile = `${process.env.PUBLIC_URL}image/img_profile.jpg`;

function Profile(props) {
  const [expanded, setExpanded] = useState(false);
  const [user, setUser] = useState();
  const [openEdit, setOpenEdit] = useState(false);
  const [editData, setEditData] = useState({ fullName: '', email: '', phone: '' });

  // Đổi mật khẩu
  const [openChangePw, setOpenChangePw] = useState(false);
  const [pwData, setPwData] = useState({ oldPassword: '', newPassword: '', confirmPassword: '' });

  useEffect(() => {
    // Lấy mã người dùng hiện tại từ localStorage
    const maNguoiDung = localStorage.getItem('userId');
    axios
      .get('https://dx.hoangphucthanh.vn:3000/warehouse/accounts')
      .then((res) => {
        const allUsers = res.data.data;
        // Tìm user theo mã người dùng
        const foundUser = allUsers.find(u => u.MaNguoiDung === maNguoiDung);
        setUser(foundUser);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const handleOpenEdit = () => {
    setEditData({ fullName: user.fullName, email: user.email, phone: user.phone });
    setOpenEdit(true);
  };

  const handleSaveEdit = () => {
    axios.put(`/api/v1/users/update`, editData)
      .then(res => {
        setUser(res.data.data.info);
        setOpenEdit(false);
        message.success('Cập nhật thông tin thành công!');
      })
      .catch(err => {
        message.error('Cập nhật thất bại!');
      });
  };

  // Đổi mật khẩu
  const handleOpenChangePw = () => {
    setPwData({ oldPassword: '', newPassword: '', confirmPassword: '' });
    setOpenChangePw(true);
  };

  const handleChangePassword = () => {
    if (!pwData.oldPassword || !pwData.newPassword || !pwData.confirmPassword) {
      message.error('Vui lòng nhập đầy đủ thông tin');
      return;
    }
    if (pwData.newPassword !== pwData.confirmPassword) {
      message.error('Mật khẩu mới không khớp');
      return;
    }
    axios.post('/api/v1/users/change-password', {
      oldPassword: pwData.oldPassword,
      newPassword: pwData.newPassword
    }).then(res => {
      setOpenChangePw(false);
      message.success('Đổi mật khẩu thành công');
    }).catch(err => {
      message.error(err.response?.data?.message || 'Đổi mật khẩu thất bại');
    });
  };

  return user === undefined ? (
    <Title level={3}>User is undefined</Title>
  ) : (
    <div style={{ padding: '24px', maxWidth: '900px', margin: '0 auto' }}>
      <Card
        style={{ marginBottom: '24px' }}
        cover={
          <div 
            style={{
              height: '200px',
              backgroundImage: `url(${imgProfile})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
        }
        actions={[
          <Button 
            key="edit" 
            icon={<EditOutlined />} 
            onClick={handleOpenEdit}
          >
            Chỉnh sửa thông tin
          </Button>,
          <Button 
            key="password" 
            type="primary" 
            danger 
            onClick={handleOpenChangePw}
          >
            Đổi mật khẩu
          </Button>,
          <Button 
            key="expand" 
            icon={<ExpandAltOutlined />}
            onClick={handleExpandClick}
          >
            {expanded ? 'Thu gọn' : 'Mở rộng'}
          </Button>
        ]}
      >
        <Meta
          avatar={<Avatar size={64} icon={<UserOutlined />} />}
          title={<Title level={3}>{user.fullName}</Title>}
          description="My monitoring profile"
        />
        
        <Divider />
        
        <Row gutter={16}>
          <Col span={24}>
            <Text strong>Account ID: </Text>
            <Text copyable>{user._id}</Text>
          </Col>
          <Col span={24} style={{ marginTop: '8px' }}>
            <Text strong>Email: </Text>
            <Text>{user.email}</Text>
          </Col>
          <Col span={24} style={{ marginTop: '8px' }}>
            <Text strong>Phone: </Text>
            <Text>{user.phone}</Text>
          </Col>
        </Row>

        {expanded && (
          <>
            <Divider />
            <Title level={4}>Chi tiết Profile:</Title>
            <Text>Đây là website quản lý smart house.</Text>
            <br />
            <Text>Thông tin chi tiết về profile người dùng.</Text>
          </>
        )}
      </Card>

      {/* Modal chỉnh sửa thông tin */}
      <Modal
        title="Chỉnh sửa thông tin"
        open={openEdit}
        onOk={handleSaveEdit}
        onCancel={() => setOpenEdit(false)}
        okText="Lưu"
        cancelText="Hủy"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>Họ tên:</Text>
            <Input
              value={editData.fullName}
              onChange={e => setEditData({ ...editData, fullName: e.target.value })}
              placeholder="Nhập họ tên"
            />
          </div>
          <div>
            <Text>Email:</Text>
            <Input
              value={editData.email}
              onChange={e => setEditData({ ...editData, email: e.target.value })}
              placeholder="Nhập email"
            />
          </div>
          <div>
            <Text>Số điện thoại:</Text>
            <Input
              value={editData.phone}
              onChange={e => setEditData({ ...editData, phone: e.target.value })}
              placeholder="Nhập số điện thoại"
            />
          </div>
        </Space>
      </Modal>

      {/* Modal đổi mật khẩu */}
      <Modal
        title="Đổi mật khẩu"
        open={openChangePw}
        onOk={handleChangePassword}
        onCancel={() => setOpenChangePw(false)}
        okText="Lưu"
        cancelText="Hủy"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>Mật khẩu cũ:</Text>
            <Input.Password
              value={pwData.oldPassword}
              onChange={e => setPwData({ ...pwData, oldPassword: e.target.value })}
              placeholder="Nhập mật khẩu cũ"
            />
          </div>
          <div>
            <Text>Mật khẩu mới:</Text>
            <Input.Password
              value={pwData.newPassword}
              onChange={e => setPwData({ ...pwData, newPassword: e.target.value })}
              placeholder="Nhập mật khẩu mới"
            />
          </div>
          <div>
            <Text>Nhập lại mật khẩu mới:</Text>
            <Input.Password
              value={pwData.confirmPassword}
              onChange={e => setPwData({ ...pwData, confirmPassword: e.target.value })}
              placeholder="Nhập lại mật khẩu mới"
            />
          </div>
        </Space>
      </Modal>
    </div>
  );
}

export default Profile;
