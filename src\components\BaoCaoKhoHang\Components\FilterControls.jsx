import React from 'react';
import { Card, Row, Col, Select, DatePicker, Button } from 'antd';

const { Option } = Select;

const FilterControls = ({
  warehouses,
  products,
  selectedWarehouse,
  setSelectedWarehouse,
  dateRange,
  setDateRange,
  selectedProduct,
  setSelectedProduct,
  onRefresh
}) => {
  // Debug current filter state
  console.log('🎛️ FilterControls current state:', {
    selectedWarehouse,
    selectedProduct,
    dateRange,
    warehousesCount: warehouses?.length,
    productsCount: products?.length
  });
  return (
    <>
      {/* Thông báo cải tiến */}
      <Card
        title="🎉 Cải tiến mới - Dashboard Báo cáo Kho hàng"
        size="small"
        style={{marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f'}}
      >
        <div style={{fontSize: '13px', color: '#389e0d'}}>
          <strong>✅ Đã hoàn thành:</strong> Nâng cấp cards thông tin với giá trị hàng nhập/xuất, phân loại tồn kho, cả<PERSON> b<PERSON><PERSON> màu s<PERSON>, và tính năng drill-down (click để xem chi tiết)
          <br />
          <strong>🔄 Đang phát triển:</strong> Hệ thống cảnh báo tồn kho nâng cao và tính năng xuất báo cáo PDF/Excel
        </div>
      </Card>

      {/* Debug Panel */}
      <Card
        title="🔍 Debug - Filter Status"
        size="small"
        style={{marginBottom: 16, backgroundColor: '#fff7e6'}}
      >
        <div style={{fontSize: '12px', color: '#666'}}>
          <strong>Kho đã chọn:</strong> {selectedWarehouse ? `${selectedWarehouse.ten_kho} (${selectedWarehouse.ma_kho})` : 'Tất cả kho'} |
          <strong> Sản phẩm:</strong> {selectedProduct ? `${selectedProduct.ten_hang} (${selectedProduct.ma_hang})` : 'Tất cả sản phẩm'} |
          <strong> Thời gian:</strong> {dateRange?.[0]?.format('DD/MM/YYYY')} - {dateRange?.[1]?.format('DD/MM/YYYY')}
        </div>
      </Card>

      <Card
        style={{
          marginBottom: 24,
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
        bodyStyle={{padding: '20px'}}
      >
      <Row gutter={16} align="middle">
        <Col span={6}>
          <div style={{marginBottom: '8px', fontSize: '14px', fontWeight: 500, color: '#666'}}>
            🏪 Chọn kho hàng
          </div>
          <Select
            showSearch
            allowClear
            style={{width:'100%'}}
            placeholder="Chọn kho"
            value={selectedWarehouse?.ma_kho}
            onChange={ma_kho => {
              if (ma_kho === 'ALL') {
                setSelectedWarehouse({ ma_kho: 'ALL', ten_kho: 'Tất cả kho' });
              } else {
                const warehouse = warehouses.find(w => w.ma_kho === ma_kho) || null;
                setSelectedWarehouse(warehouse);
              }
            }}
            optionFilterProp="children"
            size="large"
          >
            <Option key="ALL" value="ALL">Tất cả kho</Option>
            {warehouses.map(w => (
              <Option key={w.ma_kho} value={w.ma_kho}>{w.ten_kho}</Option>
            ))}
          </Select>
        </Col>
        
        <Col span={8}>
          <div style={{marginBottom: '8px', fontSize: '14px', fontWeight: 500, color: '#666'}}>
            📅 Chọn khoảng thời gian
          </div>
          <DatePicker.RangePicker
            style={{width:'100%'}}
            value={dateRange && dateRange.length === 2 ? dateRange : []}
            onChange={v => setDateRange(v || [])}
            allowClear
            size="large"
            placeholder={["Từ ngày", "Đến ngày"]}
            format="DD/MM/YYYY"
          />
        </Col>
        
        <Col span={6}>
          <div style={{marginBottom: '8px', fontSize: '14px', fontWeight: 500, color: '#666'}}>
            📦 Chọn sản phẩm
          </div>
          <Select
            showSearch
            allowClear
            style={{width:'100%'}}
            placeholder="Chọn sản phẩm"
            value={selectedProduct?.ma_hang}
            onChange={ma_hang => {
              const product = products.find(p => p.ma_hang === ma_hang) || null;
              setSelectedProduct(product);
            }}
            optionFilterProp="children"
            size="large"
          >
            {products.map(p => (
              <Option key={p.ma_hang} value={p.ma_hang}>
                {p.ma_hang} - {p.ten_hang}
              </Option>
            ))}
          </Select>
        </Col>
        
        <Col span={4}>
          <div style={{marginBottom: '8px', fontSize: '14px', fontWeight: 500, color: 'transparent'}}>
            .
          </div>
          <Button 
            type="primary"
            size="large" 
            style={{width: '100%', height: '40px'}}
            onClick={onRefresh}
          >
            Đặt lại
          </Button>
        </Col>
      </Row>
    </Card>
    </>
  );
};

export default FilterControls;
