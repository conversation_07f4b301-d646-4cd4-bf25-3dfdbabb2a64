import React, { useMemo, useState } from 'react';
import { Card, Statistic, Row, Col, Modal, Table, Tag, Button, Tooltip } from 'antd';
import {
  BarChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PieChartOutlined,
  WarningOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  AlertOutlined,
  EyeOutlined
} from '@ant-design/icons';

const SummaryCards = ({ bangNhapXuatTon }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState({ title: '', data: [], type: '' });

  // Tính toán các chỉ số nâng cao
  const calculations = useMemo(() => {
    const totalProducts = bangNhapXuatTon.length;
    const totalNhap = bangNhapXuatTon.reduce((sum, r) => sum + r.tong_nhap, 0);
    const totalXuat = bangNhapXuatTon.reduce((sum, r) => sum + r.tong_xuat, 0);
    const totalTon = bangNhapXuatTon.reduce((sum, r) => sum + r.ton_cuoi_ky, 0);

    // Giả sử giá trung bình mỗi sản phẩm là 100,000 VND (có thể lấy từ API thực tế)
    const avgPrice = 100000;
    const giaTriNhap = totalNhap * avgPrice;
    const giaTriXuat = totalXuat * avgPrice;
    const giaTriTon = totalTon * avgPrice;

    // Phân loại tồn kho
    const hetHang = bangNhapXuatTon.filter(r => r.ton_cuoi_ky <= 0);
    const sapHet = bangNhapXuatTon.filter(r => r.ton_cuoi_ky > 0 && r.ton_cuoi_ky < 10);
    const tonBinhThuong = bangNhapXuatTon.filter(r => r.ton_cuoi_ky >= 10);

    return {
      totalProducts,
      totalNhap,
      totalXuat,
      totalTon,
      giaTriNhap,
      giaTriXuat,
      giaTriTon,
      hetHang,
      sapHet,
      tonBinhThuong
    };
  }, [bangNhapXuatTon]);

  const handleCardClick = (type, title, data) => {
    setModalData({ title, data, type });
    setModalVisible(true);
  };

  const summary = useMemo(() => [
    {
      label: "Tổng mặt hàng",
      value: calculations.totalProducts,
      subValue: `${calculations.tonBinhThuong.length} bình thường`,
      icon: <BarChartOutlined />,
      color: '#e6f7ff',
      colorLight: '#f6ffed',
      textColor: '#1890ff',
      type: 'products',
      data: bangNhapXuatTon,
      status: 'normal'
    },
    {
      label: "Tổng nhập tháng",
      value: calculations.totalNhap,
      subValue: `₫${calculations.giaTriNhap.toLocaleString('vi-VN')}`,
      icon: <ArrowUpOutlined />,
      color: '#f6ffed',
      colorLight: '#f6ffed',
      textColor: '#52c41a',
      type: 'nhap',
      data: bangNhapXuatTon.filter(r => r.tong_nhap > 0),
      status: 'good'
    },
    {
      label: "Tổng xuất tháng",
      value: calculations.totalXuat,
      subValue: `₫${calculations.giaTriXuat.toLocaleString('vi-VN')}`,
      icon: <ArrowDownOutlined />,
      color: '#fff2e8',
      colorLight: '#fff1f0',
      textColor: '#ff4d4f',
      type: 'xuat',
      data: bangNhapXuatTon.filter(r => r.tong_xuat > 0),
      status: 'normal'
    },
    {
      label: "Giá trị tồn kho",
      value: calculations.totalTon,
      subValue: `₫${calculations.giaTriTon.toLocaleString('vi-VN')}`,
      icon: <DollarOutlined />,
      color: '#f9f0ff',
      colorLight: '#f9f0ff',
      textColor: '#722ed1',
      type: 'ton',
      data: bangNhapXuatTon.filter(r => r.ton_cuoi_ky > 0),
      status: 'normal'
    },
    {
      label: "Hết hàng",
      value: calculations.hetHang.length,
      subValue: "Cần nhập ngay",
      icon: <AlertOutlined />,
      color: '#fff1f0',
      colorLight: '#fff2f0',
      textColor: '#ff4d4f',
      type: 'het-hang',
      data: calculations.hetHang,
      status: 'danger'
    },
    {
      label: "Sắp hết hàng",
      value: calculations.sapHet.length,
      subValue: "Tồn < 10 đơn vị",
      icon: <WarningOutlined />,
      color: '#fffbe6',
      colorLight: '#feffe6',
      textColor: '#faad14',
      type: 'sap-het',
      data: calculations.sapHet,
      status: 'warning'
    },
  ], [bangNhapXuatTon, calculations]);

  const getStatusBorder = (status) => {
    switch(status) {
      case 'danger': return '2px solid #ff4d4f';
      case 'warning': return '2px solid #faad14';
      case 'good': return '2px solid #52c41a';
      default: return '1px solid #d9d9d9';
    }
  };

  const renderModal = () => {
    const columns = [
      { title: 'Mã hàng', dataIndex: 'ma_hang', key: 'ma_hang', width: 120 },
      { title: 'Tên hàng', dataIndex: 'ten_hang', key: 'ten_hang', width: 200 },
      { title: 'Tổng nhập', dataIndex: 'tong_nhap', key: 'tong_nhap', width: 100 },
      { title: 'Tổng xuất', dataIndex: 'tong_xuat', key: 'tong_xuat', width: 100 },
      {
        title: 'Tồn cuối kỳ',
        dataIndex: 'ton_cuoi_ky',
        key: 'ton_cuoi_ky',
        width: 120,
        render: (val) => {
          if (val <= 0) return <Tag color="red">Hết hàng ({val})</Tag>;
          if (val < 10) return <Tag color="orange">Sắp hết ({val})</Tag>;
          return <Tag color="green">Bình thường ({val})</Tag>;
        }
      },
      { title: 'Đơn vị', dataIndex: 'don_vi', key: 'don_vi', width: 100 },
    ];

    return (
      <Modal
        title={modalData.title}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            Đóng
          </Button>
        ]}
      >
        <Table
          dataSource={modalData.data}
          columns={columns}
          rowKey="ma_hang"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Modal>
    );
  };

  return (
    <>
      <Row gutter={16} style={{marginBottom: 24}}>
        {summary.map((item, idx) => (
          <Col span={4} key={idx}>
            <Tooltip title="Click để xem chi tiết">
              <Card
                style={{
                  background: `linear-gradient(135deg, ${item.color || '#f0f2f5'} 0%, ${item.colorLight || '#fafafa'} 100%)`,
                  border: getStatusBorder(item.status),
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  position: 'relative'
                }}
                bodyStyle={{padding: '20px 16px'}}
                hoverable
                onClick={() => handleCardClick(item.type, item.label, item.data)}
              >
                {/* Status indicator */}
                {item.status === 'danger' && (
                  <div style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }} />
                )}

                <Statistic
                  title={
                    <div style={{ textAlign: 'center' }}>
                      <span style={{
                        color: '#666',
                        fontSize: '13px',
                        fontWeight: 500,
                        marginBottom: '8px'
                      }}>
                        {item.label}
                      </span>
                    </div>
                  }
                  value={item.value}
                  prefix={
                    <div style={{
                      fontSize: '24px',
                      marginBottom: '4px',
                      display: 'flex',
                      justifyContent: 'center',
                      color: item.textColor
                    }}>
                      {item.icon}
                    </div>
                  }
                  suffix={
                    <EyeOutlined style={{
                      fontSize: '12px',
                      color: '#999',
                      marginLeft: '8px'
                    }} />
                  }
                  valueStyle={{
                    fontWeight: 700,
                    fontSize: '24px',
                    color: item.textColor || '#262626',
                    textAlign: 'center',
                    marginTop: '8px'
                  }}
                  formatter={(value) => value?.toLocaleString?.('vi-VN') || value}
                />

                {/* Sub value */}
                <div style={{
                  textAlign: 'center',
                  marginTop: '8px',
                  fontSize: '11px',
                  color: '#999',
                  fontWeight: 500
                }}>
                  {item.subValue}
                </div>
              </Card>
            </Tooltip>
          </Col>
        ))}
      </Row>

      {renderModal()}

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </>
  );
};

export default SummaryCards;
