import React, { useMemo } from 'react';
import dayjs from 'dayjs';
import { Card, Row, Col, Typography, Skeleton } from "antd";
import { BarChartOutlined, ArrowUpOutlined, ArrowDownOutlined, PieChartOutlined, WarningOutlined } from '@ant-design/icons';
import MiniLineChart from './MiniLineChart';

const { Text, Title } = Typography;

const SummaryCards = ({ bangNhapXuatTon, trendData = {}, monthFilter }) => {
  // monthFilter: string 'YYYY-MM' (truyền từ filter hoặc mặc định tháng hiện tại)
  const currentMonth = monthFilter || dayjs().format('YYYY-MM');
  // Nếu dữ liệu có trường month thì filter, nếu không thì lấy toàn bộ (tương thích cũ)
  const dataThisMonth = Array.isArray(bangNhapXuatTon) && bangNhapXuatTon.length > 0 && bangNhapXuatTon[0].month
    ? bangNhapXuatTon.filter(r => r.month === currentMonth)
    : bangNhapXuatTon;

  const summary = useMemo(() => [
    {
      key: 'nhap',
      label: "Tổng nhập tháng",
      value: trendData.nhapValue !== undefined ? trendData.nhapValue : dataThisMonth.reduce((sum, r) => sum + (r.tong_nhap || 0), 0),
      icon: <ArrowUpOutlined />, color: '#52c41a', chart: trendData.nhap || [], percent: trendData.nhapChange || 0,
    },
    {
      key: 'xuat',
      label: "Tổng xuất tháng",
      value: trendData.xuatValue !== undefined ? trendData.xuatValue : dataThisMonth.reduce((sum, r) => sum + (r.tong_xuat || 0), 0),
      icon: <ArrowDownOutlined />, color: '#ff4d4f', chart: trendData.xuat || [], percent: trendData.xuatChange || 0,
    },
    {
      key: 'ton',
      label: "Tổng tồn tháng",
      value: (trendData.nhapValue !== undefined && trendData.xuatValue !== undefined)
        ? (trendData.nhapValue - trendData.xuatValue)
        : dataThisMonth.reduce((sum, r) => sum + (r.ton_cuoi_ky || 0), 0),
      icon: <PieChartOutlined />, color: '#722ed1', chart: trendData.ton || [], percent: 0,
    },
    {
      key: 'mathang',
      label: "Tổng mặt hàng",
      value: trendData.mathangValue || dataThisMonth.length,
      icon: <BarChartOutlined />, color: '#1890ff', chart: trendData.mathang || [], percent: 0,
    },
    {
      key: 'hethang',
      label: "SL hết hàng",
      value: trendData.hethangValue || dataThisMonth.filter(r => (r.ton_cuoi_ky || 0) <= 0).length,
      icon: <WarningOutlined />, color: '#ff4d4f', chart: trendData.hethang || [], percent: 0,
    },
    {
      key: 'saphet',
      label: "SL sắp hết",
      value: trendData.saphetValue || dataThisMonth.filter(r => (r.ton_cuoi_ky || 0) > 0 && (r.ton_cuoi_ky || 0) < 10).length,
      icon: <WarningOutlined />, color: '#faad14', chart: trendData.saphet || [], percent: 0,
    },
  ], [dataThisMonth, trendData, currentMonth]);

  // Hiển thị loading khi đang fetch dữ liệu
  if (trendData.loading) {
    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {Array.from({ length: 6 }).map((_, idx) => (
          <Col span={4} key={idx}>
            <Card
              style={{
                borderRadius: 12,
                boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                minHeight: 120,
              }}
              bodyStyle={{ padding: 16 }}
            >
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  // Hiển thị error nếu có lỗi
  if (trendData.error) {
    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card style={{ textAlign: 'center', padding: 20 }}>
            <Text type="danger">Lỗi khi tải dữ liệu: {trendData.error}</Text>
          </Card>
        </Col>
      </Row>
    );
  }

  return (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      {summary.map((item, idx) => (
        <Col span={4} key={item.key}>
          <Card
            style={{
              borderRadius: 12,
              boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
              minHeight: 120,
            }}
            bodyStyle={{ padding: 16 }}
            hoverable
          >
            <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ fontSize: 22, color: item.color }}>{item.icon}</span>
                <Text type="secondary" style={{ fontSize: 13 }}>{item.label}</Text>
              </div>
              <Title level={3} style={{ margin: '8px 0 0 0', color: item.color, fontWeight: 700, fontSize: 24 }}>
                {item.value.toLocaleString('vi-VN')}
              </Title>
              {typeof item.percent === 'number' && idx < 3 && (
                <div style={{ margin: '2px 0 0 0', fontSize: 13 }}>
                  <Text type={
                    // Logic hiển thị màu cho các card khác nhau
                    item.key === 'nhap' ? (item.percent >= 0 ? 'success' : 'danger') :  // Nhập tăng = tốt (xanh)
                    item.key === 'xuat' ? (item.percent >= 0 ? 'danger' : 'success') :  // Xuất tăng = xấu (đỏ)
                    (item.percent >= 0 ? 'success' : 'danger')  // Tồn tăng = tốt (xanh)
                  }>
                    {item.percent >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />} {Math.abs(item.percent).toFixed(1)}% so với {trendData.comparisonText || 'tháng trước'}
                  </Text>
                </div>
              )}
              <div style={{ marginTop: 8 }}>
                <MiniLineChart data={item.chart} color={item.color} />
              </div>
            </div>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default SummaryCards;

