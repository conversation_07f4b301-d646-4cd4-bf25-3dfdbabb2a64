import React, { useMemo, useState } from 'react';
import { Card, Statistic, Row, Col, Modal, Table, Tag, Button, Tooltip } from 'antd';
import {
  Bar<PERSON>hartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PieChartOutlined,
  WarningOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  AlertOutlined,
  EyeOutlined
} from '@ant-design/icons';

const SummaryCards = ({ bangNhapXuatTon, stockIn, stockOut, selectedWarehouse, dateRange, selectedProduct }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState({ title: '', data: [], type: '' });

  // Debug dữ liệu đầu vào
  console.log('📊 SummaryCards received data:', {
    dataLength: bangNhapXuatTon?.length,
    stockInLength: stockIn?.length,
    stockOutLength: stockOut?.length,
    sampleData: bangNhapXuatTon?.[0],
    sampleStockIn: stockIn?.[0],
    allFields: bangNhapXuatTon?.[0] ? Object.keys(bangNhapXuatTon[0]) : []
  });

  // Tính toán các chỉ số nâng cao
  const calculations = useMemo(() => {
    const totalProducts = bangNhapXuatTon.length;

    // Tính tổng nhập/xuất theo tháng hiện tại từ stockIn/stockOut
    const currentMonth = new Date().getMonth() + 1; // 1-12
    const currentYear = new Date().getFullYear();

    // Filter stockIn theo tháng hiện tại
    const nhapThangNay = stockIn?.filter(item => {
      if (!item.ngay_nhap_hang) return false;
      const date = new Date(item.ngay_nhap_hang);
      return date.getMonth() + 1 === currentMonth && date.getFullYear() === currentYear;
    }) || [];

    // Filter stockOut theo tháng hiện tại
    const xuatThangNay = stockOut?.filter(item => {
      if (!item.ngay_xuat_hang) return false;
      const date = new Date(item.ngay_xuat_hang);
      return date.getMonth() + 1 === currentMonth && date.getFullYear() === currentYear;
    }) || [];

    // Tính tổng số lượng nhập/xuất tháng này
    const totalNhapThangNay = nhapThangNay.reduce((sum, item) => sum + (item.so_luong || 0), 0);
    const totalXuatThangNay = xuatThangNay.reduce((sum, item) => sum + (item.so_luong || 0), 0);

    // Tính tồn tháng này = Tồn đầu kỳ + Nhập tháng này - Xuất tháng này
    // Lấy tồn đầu kỳ từ bangNhapXuatTon (tồn cuối kỳ hiện tại)
    const tonDauKy = bangNhapXuatTon.reduce((sum, r) => sum + r.ton_cuoi_ky, 0);

    // Tồn tháng này = Tồn hiện tại + Nhập tháng này - Xuất tháng này
    // Hoặc đơn giản hơn: nếu chỉ tính riêng tháng này thì = Nhập - Xuất
    const totalTonThangNay = totalNhapThangNay - totalXuatThangNay;

    // Giả sử giá trung bình mỗi sản phẩm là 100,000 VND (có thể lấy từ API thực tế)
    const avgPrice = 100000;
    const giaTriNhapThangNay = totalNhapThangNay * avgPrice;
    const giaTriXuatThangNay = totalXuatThangNay * avgPrice;
    const giaTriTonThangNay = totalTonThangNay * avgPrice;

    // Phân loại tồn kho
    const hetHang = bangNhapXuatTon.filter(r => r.ton_cuoi_ky <= 0);
    const sapHet = bangNhapXuatTon.filter(r => r.ton_cuoi_ky > 0 && r.ton_cuoi_ky < 10);
    const tonBinhThuong = bangNhapXuatTon.filter(r => r.ton_cuoi_ky >= 10);

    console.log('📊 Calculations Debug:', {
      currentMonth,
      currentYear,
      nhapThangNayLength: nhapThangNay.length,
      xuatThangNayLength: xuatThangNay.length,
      totalNhapThangNay,
      totalXuatThangNay,
      tonDauKy,
      totalTonThangNay,
      calculation: `${totalNhapThangNay} - ${totalXuatThangNay} = ${totalTonThangNay}`
    });

    return {
      totalProducts,
      totalNhapThangNay,
      totalXuatThangNay,
      totalTonThangNay,
      giaTriNhapThangNay,
      giaTriXuatThangNay,
      giaTriTonThangNay,
      hetHang,
      sapHet,
      tonBinhThuong,
      nhapThangNay,
      xuatThangNay
    };
  }, [bangNhapXuatTon, stockIn, stockOut]);

  const handleCardClick = (type, title, data) => {
    console.log('🖱️ Card clicked:', { type, title, dataLength: data?.length });
    setModalData({ title, data, type });
    setModalVisible(true);
  };

  const summary = useMemo(() => [
    {
      label: "Tổng mặt hàng",
      value: calculations.totalProducts,
      subValue: `${calculations.tonBinhThuong.length} bình thường`,
      icon: <BarChartOutlined />,
      color: '#e6f7ff',
      colorLight: '#f6ffed',
      textColor: '#1890ff',
      type: 'products',
      data: bangNhapXuatTon,
      status: 'normal'
    },
    {
      label: "Tổng nhập tháng",
      value: calculations.totalNhapThangNay,
      subValue: `₫${calculations.giaTriNhapThangNay.toLocaleString('vi-VN')}`,
      icon: <ArrowUpOutlined />,
      color: '#f6ffed',
      colorLight: '#f6ffed',
      textColor: '#52c41a',
      type: 'nhap',
      data: calculations.nhapThangNay,
      status: 'good'
    },
    {
      label: "Tổng xuất tháng",
      value: calculations.totalXuatThangNay,
      subValue: `₫${calculations.giaTriXuatThangNay.toLocaleString('vi-VN')}`,
      icon: <ArrowDownOutlined />,
      color: '#fff2e8',
      colorLight: '#fff1f0',
      textColor: '#ff4d4f',
      type: 'xuat',
      data: calculations.xuatThangNay,
      status: 'normal'
    },
    {
      label: "Tổng tồn tháng",
      value: calculations.totalTonThangNay,
      subValue: `₫${calculations.giaTriTonThangNay.toLocaleString('vi-VN')}`,
      icon: <DollarOutlined />,
      color: '#f9f0ff',
      colorLight: '#f9f0ff',
      textColor: '#722ed1',
      type: 'ton-thang',
      data: bangNhapXuatTon.filter(r => r.ton_cuoi_ky > 0),
      status: calculations.totalTonThangNay >= 0 ? 'good' : 'danger'
    },
    {
      label: "Hết hàng",
      value: calculations.hetHang.length,
      subValue: "Cần nhập ngay",
      icon: <AlertOutlined />,
      color: '#fff1f0',
      colorLight: '#fff2f0',
      textColor: '#ff4d4f',
      type: 'het-hang',
      data: calculations.hetHang,
      status: 'danger'
    },
    {
      label: "Sắp hết hàng",
      value: calculations.sapHet.length,
      subValue: "Tồn < 10 đơn vị",
      icon: <WarningOutlined />,
      color: '#fffbe6',
      colorLight: '#feffe6',
      textColor: '#faad14',
      type: 'sap-het',
      data: calculations.sapHet,
      status: 'warning'
    },
  ], [bangNhapXuatTon, calculations]);

  const getStatusBorder = (status) => {
    switch(status) {
      case 'danger': return '2px solid #ff4d4f';
      case 'warning': return '2px solid #faad14';
      case 'good': return '2px solid #52c41a';
      default: return '1px solid #d9d9d9';
    }
  };

  const renderModal = () => {
    // Columns khác nhau cho từng loại dữ liệu
    let columns = [];

    if (modalData.type === 'nhap' || modalData.type === 'xuat') {
      // Columns cho stockIn/stockOut
      columns = [
        { title: 'Mã hàng', dataIndex: 'ma_hang', key: 'ma_hang', width: 120 },
        { title: 'Tên hàng', dataIndex: 'ten_hang', key: 'ten_hang', width: 200 },
        { title: 'Số lượng', dataIndex: 'so_luong', key: 'so_luong', width: 100 },
        {
          title: modalData.type === 'nhap' ? 'Ngày nhập' : 'Ngày xuất',
          dataIndex: modalData.type === 'nhap' ? 'ngay_nhap_hang' : 'ngay_xuat_hang',
          key: 'ngay',
          width: 120,
          render: (val) => val ? new Date(val).toLocaleDateString('vi-VN') : 'N/A'
        },
        { title: 'Kho', dataIndex: 'ten_kho', key: 'ten_kho', width: 150 },
        { title: 'Đơn vị', dataIndex: 'don_vi', key: 'don_vi', width: 100 },
      ];
    } else {
      // Columns cho bangNhapXuatTon
      columns = [
        { title: 'Mã hàng', dataIndex: 'ma_hang', key: 'ma_hang', width: 120 },
        { title: 'Tên hàng', dataIndex: 'ten_hang', key: 'ten_hang', width: 200 },
        { title: 'Tổng nhập', dataIndex: 'tong_nhap', key: 'tong_nhap', width: 100 },
        { title: 'Tổng xuất', dataIndex: 'tong_xuat', key: 'tong_xuat', width: 100 },
        {
          title: 'Tồn cuối kỳ',
          dataIndex: 'ton_cuoi_ky',
          key: 'ton_cuoi_ky',
          width: 120,
          render: (val) => {
            if (val <= 0) return <Tag color="red">Hết hàng ({val})</Tag>;
            if (val < 10) return <Tag color="orange">Sắp hết ({val})</Tag>;
            return <Tag color="green">Bình thường ({val})</Tag>;
          }
        },
        { title: 'Đơn vị', dataIndex: 'don_vi', key: 'don_vi', width: 100 },
      ];
    }

    return (
      <Modal
        title={`${modalData.title} (${modalData.data?.length || 0} mặt hàng)`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            Đóng
          </Button>
        ]}
      >
        {modalData.data?.length > 0 ? (
          <Table
            dataSource={modalData.data}
            columns={columns}
            rowKey={(record, index) => record.ma_hang || `item-${index}`}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            Không có dữ liệu để hiển thị
          </div>
        )}
      </Modal>
    );
  };

  return (
    <>
      {/* Debug Test Button */}
      <div style={{ marginBottom: 16, textAlign: 'center' }}>
        <Button
          type="primary"
          size="small"
          onClick={() => handleCardClick('test', 'Test Modal', bangNhapXuatTon.slice(0, 5))}
        >
          🧪 Test Modal (Debug)
        </Button>
      </div>

      <Row gutter={16} style={{marginBottom: 24}}>
        {summary.map((item, idx) => (
          <Col span={4} key={idx}>
            <Tooltip title="Click để xem chi tiết">
              <Card
                style={{
                  background: `linear-gradient(135deg, ${item.color || '#f0f2f5'} 0%, ${item.colorLight || '#fafafa'} 100%)`,
                  border: getStatusBorder(item.status),
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  position: 'relative'
                }}
                bodyStyle={{padding: '20px 16px'}}
                hoverable
                onClick={(e) => {
                  e.stopPropagation();
                  handleCardClick(item.type, item.label, item.data);
                }}
              >
                {/* Status indicator */}
                {item.status === 'danger' && (
                  <div style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }} />
                )}

                <Statistic
                  title={
                    <div style={{ textAlign: 'center' }}>
                      <span style={{
                        color: '#666',
                        fontSize: '13px',
                        fontWeight: 500,
                        marginBottom: '8px'
                      }}>
                        {item.label}
                      </span>
                    </div>
                  }
                  value={item.value}
                  prefix={
                    <div style={{
                      fontSize: '24px',
                      marginBottom: '4px',
                      display: 'flex',
                      justifyContent: 'center',
                      color: item.textColor
                    }}>
                      {item.icon}
                    </div>
                  }
                  suffix={
                    <EyeOutlined style={{
                      fontSize: '12px',
                      color: '#999',
                      marginLeft: '8px'
                    }} />
                  }
                  valueStyle={{
                    fontWeight: 700,
                    fontSize: '24px',
                    color: item.textColor || '#262626',
                    textAlign: 'center',
                    marginTop: '8px'
                  }}
                  formatter={(value) => value?.toLocaleString?.('vi-VN') || value}
                />

                {/* Sub value */}
                <div style={{
                  textAlign: 'center',
                  marginTop: '8px',
                  fontSize: '11px',
                  color: '#999',
                  fontWeight: 500
                }}>
                  {item.subValue}
                </div>
              </Card>
            </Tooltip>
          </Col>
        ))}
      </Row>

      {renderModal()}

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </>
  );
};

export default SummaryCards;
