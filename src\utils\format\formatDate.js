// Date formatting utilities
export const formatDate = (date, format = 'DD/MM/YYYY') => {
    if (!date) return '';
    
    try {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        
        switch (format) {
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'MM/DD/YYYY':
                return `${month}/${day}/${year}`;
            default:
                return d.toLocaleDateString('vi-VN');
        }
    } catch (error) {
        console.error('Date formatting error:', error);
        return '';
    }
};

// Export default cũng để tương thích
export default formatDate;
