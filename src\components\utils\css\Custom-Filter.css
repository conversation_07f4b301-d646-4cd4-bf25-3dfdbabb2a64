/* <PERSON><PERSON><PERSON> bộ lọc và tìm kiếm */
.filters {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    margin-bottom: 20px;
    align-items: center;  /* Đ<PERSON><PERSON> bảo các phần tử căn chỉnh đúng hàng */
}

/* Input tìm kiếm */
.filters .ant-input {
    width: 300px;
    height: 35px;
    padding: 0 12px;
    font-size: 13px;
    border-radius: 6px;
    color: #000 !important;           /* 🔥 <PERSON>àu chữ đen khi nhập */
    text-align: left;                 /* 🔥 Căn trái nội dung */
}

.filters .ant-input-affix-wrapper {
    height: 35px;
    border-radius: 6px;
    align-items: center;
    justify-content: space-between;
    text-align: left;                  /* 🔥 Căn trái */
}

/* <PERSON><PERSON><PERSON> bảo select filter luôn canh giữa theo chiều dọc */
.filters .ant-select {
    display: flex;
    align-items: center;
    height: 40px;
}

/* Selector canh gi<PERSON>a và không bị thụt */
.filters .ant-select-selector {
    display: flex !important;
    align-items: center !important;
    height: 35px !important;
    padding: 0 12px !important;
    background-color: #28a745 !important;
    color: white !important;
    font-weight: 600;
    border-radius: 6px !important;
    border: none !important;
    box-sizing: border-box;
}

/* Nội dung item đã chọn */
.filters .ant-select-selection-item {
    color: white !important;
    font-weight: 600 !important;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: left;
    height: 100%;
}

/* Mũi tên canh giữa */
.filters .ant-select-arrow {
    color: white !important;           /* Màu trắng đậm */
    font-weight: bold !important;       /* Đậm hơn */
    font-size: 14px !important;        /* Tăng kích thước nếu muốn */
    display: flex;
}

/* Đảm bảo container filter không bị lệch */
.filters {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

/* Nút làm mới */
.filters .ant-btn {
    background-color: #28a745;
    color: white;
    font-size: 14px;
    font-weight: 600;
    height: 35px;
    border-radius: 6px;
    padding: 0 12px;
}

/* Nút làm mới khi hover */
.filters .ant-select-selector:hover,
.filters .ant-select-selection-item:hover,
.filters .ant-btn:hover {
    background-color: #218838 !important;
    border-radius: 6px;
}

/* Media queries cho responsive design */
@media (max-width: 1200px) {
    .filters .ant-input {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .filters {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .filters .ant-input,
    .filters .ant-input-affix-wrapper {
        width: 100% !important;
    }

    .filters .ant-select {
        width: 100% !important;
    }

    .filters .ant-btn {
        width: 100%;
        margin-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .filters {
        gap: 0.25rem;
    }

    .filters .ant-input {
        font-size: 12px;
        height: 32px;
    }

    .filters .ant-select-selector {
        height: 32px !important;
        font-size: 12px;
    }

    .filters .ant-btn {
        height: 32px;
        font-size: 12px;
    }
}