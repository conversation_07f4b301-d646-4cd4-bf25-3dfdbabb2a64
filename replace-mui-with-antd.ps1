# PowerShell script to replace Material-UI imports with Ant Design

# Function to replace Material-UI imports with Ant Design
function Replace-MUIWithAntD {
    param($FilePath)
    
    if (Test-Path $FilePath) {
        Write-Host "Processing: $FilePath"
        
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        
        # Replace common Material-UI imports with Ant Design
        $content = $content -replace '@mui/material', 'antd'
        $content = $content -replace 'import { ([^}]+) } from [''"]@mui/material[''"];?', 'import { $1 } from "antd";'
        $content = $content -replace 'import { ([^}]+) } from [''"]antd[''"];?', 'import { $1 } from "antd";'
        
        # Replace common components
        $content = $content -replace '\bBox\b', 'div'
        $content = $content -replace '\bContainer\b', 'div'
        $content = $content -replace '\bGrid\b', 'Row'
        $content = $content -replace '\bPaper\b', 'Card'
        $content = $content -replace '\bDialog\b', 'Modal'
        $content = $content -replace '\bDialogTitle\b', 'Modal.title'
        $content = $content -replace '\bDialogContent\b', 'Modal.content'
        $content = $content -replace '\bDialogActions\b', 'Modal.footer'
        $content = $content -replace '\bTextField\b', 'Input'
        $content = $content -replace '\bMenuItem\b', 'Select.Option'
        $content = $content -replace '\bFormControl\b', 'div'
        $content = $content -replace '\bInputLabel\b', 'label'
        $content = $content -replace '\bCircularProgress\b', 'Spin'
        
        # Replace common props
        $content = $content -replace 'sx=\{[^}]+\}', 'style={}'
        $content = $content -replace 'variant=[''"]([^''"]+)[''"]', ''
        $content = $content -replace 'color=[''"]([^''"]+)[''"]', ''
        $content = $content -replace 'maxWidth=[''"]([^''"]+)[''"]', ''
        $content = $content -replace 'fullWidth', ''
        
        # Save the modified content
        Set-Content $FilePath $content -Encoding UTF8
        Write-Host "Updated: $FilePath"
    }
}

# Get all JS and JSX files in the components directory
$files = Get-ChildItem -Path "d:\Workspace\PROJECT\DXHOPT\src\components" -Include "*.js", "*.jsx" -Recurse

Write-Host "Found $($files.Count) files to process"

foreach ($file in $files) {
    Replace-MUIWithAntD -FilePath $file.FullName
}

Write-Host "Completed processing all files!"
