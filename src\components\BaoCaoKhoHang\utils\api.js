﻿import axios from 'axios';

const BASE_URL = 'https://dx.hoangphucthanh.vn:3000';

// API endpoints cho warehouse
export const warehouseAPI = {
  // Stock In/Out APIs
  getStockIn: () => axios.get(`${BASE_URL}/warehouse/stock-in`),
  getStockOut: () => axios.get(`${BASE_URL}/warehouse/stock-out`),
  getInventory: () => axios.get(`${BASE_URL}/warehouse/inventory`),
  
  // Master data APIs
  getProducts: () => axios.get(`${BASE_URL}/warehouse/products`),
  getWarehouses: () => axios.get(`${BASE_URL}/warehouse/warehouses`),
  getSuppliers: () => axios.get(`${BASE_URL}/warehouse/suppliers`),
  getCustomers: () => axios.get(`${BASE_URL}/warehouse/customers`),
  
  // Contract and Order APIs
  getContracts: () => axios.get(`${BASE_URL}/warehouse/contracts`),
  getOrderDetails: () => axios.get(`${BASE_URL}/warehouse/order-details`),
  getBills: () => axios.get(`${BASE_URL}/warehouse/bills`),
  
  // Product type APIs
  getProductTypes: () => axios.get(`${BASE_URL}/warehouse/product-types`),
  
  // Account APIs
  getAccounts: () => axios.get(`${BASE_URL}/warehouse/accounts`),
  getRoles: () => axios.get(`${BASE_URL}/warehouse/roles`),
};

// API endpoints cho CRM
export const crmAPI = {
  // Customer management
  getPotentialCustomers: () => axios.get(`${BASE_URL}/crm/potential-customers`),
  getCustomerGroups: () => axios.get(`${BASE_URL}/crm/customer-groups`),
  getOpportunitySources: () => axios.get(`${BASE_URL}/crm/opportunity-sources`),
  
  // Quotation management
  getQuotations: () => axios.get(`${BASE_URL}/crm/quotations`),
  getQuotationTypes: () => axios.get(`${BASE_URL}/crm/quotation-types`),
  getQuotationStatuses: () => axios.get(`${BASE_URL}/crm/quotation-statuses`),
  
  // Interaction management
  getCustomerInteractions: () => axios.get(`${BASE_URL}/crm/customer-interactions`),
  getInteractionTypes: () => axios.get(`${BASE_URL}/crm/interaction-types`),
};

// Utility function để xử lý response
export const handleAPIResponse = (response) => {
  if (response.data && response.data.success) {
    return response.data.data || [];
  }
  throw new Error(response.data?.message || 'API call failed');
};

// Utility function để xử lý error
export const handleAPIError = (error) => {
  console.error('API Error:', error);
  if (error.response) {
    throw new Error(error.response.data?.message || `HTTP ${error.response.status}: ${error.response.statusText}`);
  } else if (error.request) {
    throw new Error('Không thể kết nối đến server');
  } else {
    throw new Error(error.message);
  }
};

