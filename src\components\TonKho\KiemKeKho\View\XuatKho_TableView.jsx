import React from 'react';
import { Table } from 'antd';
import { getXuatKhoColumns } from './XuatKho_Columns';
import '../XuatKho_Main.css';

const XuatKhoTableView = ({
    data,
    currentPage,
    pageSize,
    loading,
    handleEdit,
    handleRemove,
    canEdit,
    onSortChange,
    sortField,
    sortOrder,
}) => {
    // Lấy columns gốc
    let columns = getXuatKhoColumns(handleEdit, handleRemove, canEdit);

    // Gắn sortOrder cho đúng cột đang sort
    columns = columns.map(col =>
        col.key === sortField
            ? { ...col, sortOrder: sortOrder || undefined }
            : { ...col, sortOrder: undefined }
    );

    return (
        <div className="bang-xuat-kho-scroll-wrapper">
            <Table
                columns={columns}
                dataSource={data.slice((currentPage - 1) * pageSize, currentPage * pageSize)}
                rowKey="ma_stock-out"
                bordered
                size="small"
                pagination={false}
                className="custom-ant-table"
                loading={loading}
                scroll={{ x: 'max-content' }} // Cho phép cuộn ngang khi cần thiết
                onChange={(_, __, sorter) => {
                    if (sorter && sorter.columnKey && sorter.order) {
                        onSortChange && onSortChange(sorter.columnKey, sorter.order);
                    } else {
                        onSortChange && onSortChange('ngay_xuat_hang', 'descend');
                    }
                }}
                sortDirections={['descend', 'ascend']}
            />
        </div>
    );
};

export default XuatKhoTableView;
