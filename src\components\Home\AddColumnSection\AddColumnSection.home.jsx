import React from "react";
import { Input, Button, Space } from "antd";

function AddColumnSection({ newField, setNewField, handleAddColumn }) {
  return (
    <div
      style={{
        marginTop: "24px",
        display: "flex",
        gap: "16px",
        flexWrap: "wrap",
      }}
    >
      <Input
        placeholder="Nhập tên trường mới"
        value={newField}
        onChange={(e) => setNewField(e.target.value)}
        style={{
          flex: 1,
          minWidth: "200px",
        }}
      />
      <Button
        type="primary"
        onClick={handleAddColumn}
        style={{
          minWidth: 150,
        }}
      >
        Thêm trường
      </Button>
    </div>
  );
}

export default AddColumnSection;