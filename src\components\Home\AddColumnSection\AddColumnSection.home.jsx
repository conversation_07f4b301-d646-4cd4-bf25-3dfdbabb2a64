import React from "react";
import { Input, Button, Space } from "antd";

function AddColumnSection({ newField, setNewField, handleAddColumn, isMobile }) {
  return (
    <Space
      direction={isMobile ? "vertical" : "horizontal"}
      size="middle"
      style={{
        marginTop: 24,
        width: "100%",
      }}
    >
      <Input
        placeholder="Nhập tên trường mới"
        value={newField}
        onChange={(e) => setNewField(e.target.value)}
        style={{
          flex: 1,
          fontSize: isMobile ? "14px" : "16px",
        }}
      />
      <Button
        type="primary"
        onClick={handleAddColumn}
        style={{
          minWidth: isMobile ? "100%" : 150,
          fontSize: isMobile ? "12px" : "14px",
        }}
      >
        Thêm trường
      </Button>
    </Space>
  );
}

export default AddColumnSection;