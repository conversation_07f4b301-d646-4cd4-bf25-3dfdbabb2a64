import React, { useEffect, useState } from "react";
import { Card, Typography, Row, Col, Select, Divider } from "antd";
import axios from "axios";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts";

// TODO: This component needs to be fully migrated to Ant Design
// Temporarily returning a simplified version

export default function BaoCaoKhoHang_Main() {
  const [warehouses, setWarehouses] = useState([]);
  const [products, setProducts] = useState([]);
  const [stockIn, setStockIn] = useState([]);
  const [stockOut, setStockOut] = useState([]);
  const [inventory, setInventory] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  useEffect(() => {
    // Lấy dữ liệu song song
    Promise.all([
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/warehouses"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/products"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-in"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-out"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/inventory"),
    ]).then(([wRes, pRes, inRes, outRes, invRes]) => {
      setWarehouses(wRes.data.data || []);
      setProducts(pRes.data.data || []);
      setStockIn(inRes.data.data || []);
      setStockOut(outRes.data.data || []);
      setInventory(invRes.data.data || []);
    });
  }, []);

  // Tổng hợp nhập-xuất-tồn cho từng sản phẩm trong kho được chọn
  const tongHopNhapXuatTon = () => {
    if (!selectedWarehouse) return [];
    // Lọc nhập/xuất/tồn theo kho
    const ma_kho = selectedWarehouse.ma_kho;
    const nhap = stockIn.filter(i => i.ten_kho === ma_kho);
    const xuat = stockOut.filter(o => o.ten_kho === ma_kho);
    const ton = inventory.filter(t => t.ten_kho === ma_kho);

    // Tổng hợp theo mã hàng
    return products.map(prod => {
      const nhapHang = nhap.filter(i => i.ma_hang === prod.ma_hang);
      const xuatHang = xuat.filter(o => o.ma_hang === prod.ma_hang);
      const tonHang = ton.find(t => t.ma_hang === prod.ma_hang);

      const tongNhap = nhapHang.reduce((sum, i) => sum + (i.so_luong_nhap || 0), 0);
      const tongXuat = xuatHang.reduce((sum, o) => sum + (o.so_luong_xuat || 0), 0);
      const tonCuoiKy = tonHang ? tonHang.ton_hien_tai : (tongNhap - tongXuat);

      return {
        ten_hang: prod.ten_hang,
        ma_hang: prod.ma_hang,
        tong_nhap: tongNhap,
        tong_xuat: tongXuat,
        ton_cuoi_ky: tonCuoiKy,
        don_vi: prod.don_vi_ban_hang,
      };
    }).filter(row => row.tong_nhap > 0 || row.tong_xuat > 0 || row.ton_cuoi_ky > 0);
  };

  const bangNhapXuatTon = tongHopNhapXuatTon();

  return (
    <div style={{ padding: 24 }}>
      <Typography.Title level={3} style={{ marginBottom: 16 }}>
        BÁO CÁO NHẬP - XUẤT - TỒN KHO
      </Typography.Title>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} md={8}>
          <Select
            style={{ width: '100%' }}
            placeholder="Chọn kho"
            value={selectedWarehouse?.ma_kho}
            onChange={(value) => {
              const warehouse = warehouses.find(w => w.ma_kho === value);
              setSelectedWarehouse(warehouse);
            }}
            options={warehouses.map(w => ({
              value: w.ma_kho,
              label: w.ten_kho || ""
            }))}
          />
        </Col>
      </Row>
      <Divider style={{ margin: '16px 0' }} />
      <Card>
        <Typography.Title level={4} style={{ marginBottom: 16 }}>
          Bảng tổng hợp nhập - xuất - tồn kho
        </Typography.Title>
        <Row gutter={[16, 8]} style={{ fontWeight: 600, marginBottom: 8, borderBottom: '1px solid #f0f0f0', paddingBottom: 8 }}>
          <Col span={5}>Mã hàng</Col>
          <Col span={5}>Tên hàng</Col>
          <Col span={4}>Tổng nhập</Col>
          <Col span={4}>Tổng xuất</Col>
          <Col span={4}>Tồn cuối kỳ</Col>
          <Col span={2}>Cảnh báo</Col>
        </Row>
        {bangNhapXuatTon.map((row, idx) => {
          let warning = null;
          if (row.ton_cuoi_ky <= 0) {
            warning = <Typography.Text type="danger">Hết hàng!</Typography.Text>;
          } else if (row.ton_cuoi_ky < 10) {
            warning = <Typography.Text type="warning">Sắp hết hàng</Typography.Text>;
          }
          return (
            <Row gutter={[16, 8]} key={idx} style={{ padding: '8px 0', borderBottom: '1px solid #f5f5f5' }}>
              <Col span={5}>{row.ma_hang}</Col>
              <Col span={5}>{row.ten_hang}</Col>
              <Col span={4}>{row.tong_nhap}</Col>
              <Col span={4}>{row.tong_xuat}</Col>
              <Col span={4}>{row.ton_cuoi_ky}</Col>
              <Col span={2}>{warning}</Col>
            </Row>
          );
        })}
      </Card>

      <Divider style={{ margin: '16px 0' }} />

      <Typography.Title level={4} style={{ marginBottom: 16 }}>
        Biểu đồ nhập - xuất - tồn theo mã hàng
      </Typography.Title>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={bangNhapXuatTon}>
          <XAxis dataKey="ma_hang" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="tong_nhap" fill="#1976d2" name="Tổng nhập" />
          <Bar dataKey="tong_xuat" fill="#d32f2f" name="Tổng xuất" />
          <Bar dataKey="ton_cuoi_ky" fill="#388e3c" name="Tồn cuối kỳ" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}