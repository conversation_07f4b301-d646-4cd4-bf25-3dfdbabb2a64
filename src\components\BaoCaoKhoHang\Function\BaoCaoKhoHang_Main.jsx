import React, { useEffect, useState } from "react";
import { Box, Typography, Card, CardContent, Grid, Autocomplete, TextField, Divider } from "@mui/material";
import axios from "axios";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts";

export default function BaoCaoKhoHang_Main() {
  const [warehouses, setWarehouses] = useState([]);
  const [products, setProducts] = useState([]);
  const [stockIn, setStockIn] = useState([]);
  const [stockOut, setStockOut] = useState([]);
  const [inventory, setInventory] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  useEffect(() => {
    // Lấy dữ liệu song song
    Promise.all([
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/warehouses"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/products"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-in"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-out"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/inventory"),
    ]).then(([wRes, pRes, inRes, outRes, invRes]) => {
      setWarehouses(wRes.data.data || []);
      setProducts(pRes.data.data || []);
      setStockIn(inRes.data.data || []);
      setStockOut(outRes.data.data || []);
      setInventory(invRes.data.data || []);
    });
  }, []);

  // Tổng hợp nhập-xuất-tồn cho từng sản phẩm trong kho được chọn
  const tongHopNhapXuatTon = () => {
    if (!selectedWarehouse) return [];
    // Lọc nhập/xuất/tồn theo kho
    const ma_kho = selectedWarehouse.ma_kho;
    const nhap = stockIn.filter(i => i.ten_kho === ma_kho);
    const xuat = stockOut.filter(o => o.ten_kho === ma_kho);
    const ton = inventory.filter(t => t.ten_kho === ma_kho);

    // Tổng hợp theo mã hàng
    return products.map(prod => {
      const nhapHang = nhap.filter(i => i.ma_hang === prod.ma_hang);
      const xuatHang = xuat.filter(o => o.ma_hang === prod.ma_hang);
      const tonHang = ton.find(t => t.ma_hang === prod.ma_hang);

      const tongNhap = nhapHang.reduce((sum, i) => sum + (i.so_luong_nhap || 0), 0);
      const tongXuat = xuatHang.reduce((sum, o) => sum + (o.so_luong_xuat || 0), 0);
      const tonCuoiKy = tonHang ? tonHang.ton_hien_tai : (tongNhap - tongXuat);

      return {
        ten_hang: prod.ten_hang,
        ma_hang: prod.ma_hang,
        tong_nhap: tongNhap,
        tong_xuat: tongXuat,
        ton_cuoi_ky: tonCuoiKy,
        don_vi: prod.don_vi_ban_hang,
      };
    }).filter(row => row.tong_nhap > 0 || row.tong_xuat > 0 || row.ton_cuoi_ky > 0);
  };

  const bangNhapXuatTon = tongHopNhapXuatTon();

  return (
    <Box sx={{ p: { xs: 1, md: 3 } }}>
      <Typography variant="h5" fontWeight={700} mb={2}>BÁO CÁO NHẬP - XUẤT - TỒN KHO</Typography>
      <Grid container spacing={2} mb={2}>
        <Grid item xs={12} md={4}>
          <Autocomplete
            options={warehouses}
            getOptionLabel={w => w.ten_kho || ""}
            value={selectedWarehouse}
            onChange={(_, v) => setSelectedWarehouse(v)}
            renderInput={params => <TextField {...params} label="Chọn kho" />}
          />
        </Grid>
      </Grid>
      <Divider sx={{ my: 2 }} />
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Bảng tổng hợp nhập - xuất - tồn kho</Typography>
          <Grid container spacing={2} sx={{ fontWeight: 600, mb: 1 }} alignItems="center">
            <Grid item xs={3}>Mã hàng</Grid>
            <Grid item xs={3}>Tên hàng</Grid>
            <Grid item xs={2}>Tổng nhập</Grid>
            <Grid item xs={2}>Tổng xuất</Grid>
            <Grid item xs={2}>Tồn cuối kỳ</Grid>
          </Grid>
          {bangNhapXuatTon.map((row, idx) => {
            let warning = null;
            if (row.ton_cuoi_ky <= 0) {
              warning = <span style={{ color: "red", fontWeight: 600 }}>Cảnh báo: Hết hàng!</span>;
            } else if (row.ton_cuoi_ky < 10) {
              warning = <span style={{ color: "orange", fontWeight: 600 }}>Cảnh báo: Sắp hết hàng</span>;
            }
            return (
              <Grid container spacing={2} key={idx} alignItems="center">
                <Grid item xs={3}>{row.ma_hang}</Grid>
                <Grid item xs={3}>{row.ten_hang}</Grid>
                <Grid item xs={2}>{row.tong_nhap}</Grid>
                <Grid item xs={2}>{row.tong_xuat}</Grid>
                <Grid item xs={2}>
                  {row.ton_cuoi_ky}
                  {warning && <div>{warning}</div>}
                </Grid>
              </Grid>
            );
          })}
        </CardContent>
      </Card>
      <Divider sx={{ my: 2 }} />
      <Typography variant="h6" gutterBottom>Biểu đồ nhập - xuất - tồn theo mã hàng</Typography>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={bangNhapXuatTon}>
          <XAxis dataKey="ma_hang" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="tong_nhap" fill="#1976d2" name="Tổng nhập" />
          <Bar dataKey="tong_xuat" fill="#d32f2f" name="Tổng xuất" />
          <Bar dataKey="ton_cuoi_ky" fill="#388e3c" name="Tồn cuối kỳ" />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
}