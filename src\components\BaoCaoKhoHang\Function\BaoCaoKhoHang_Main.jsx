import React, { useEffect, useState } from "react";
import { 
  Card, 
  Typography, 
  Row, 
  Col, 
  Select, 
  Divider, 
  Statistic, 
  Table, 
  Tag, 
  DatePicker, 
  Button, 
  Space,
  Badge,
  Spin
} from "antd";
import { 
  TrendingUpOutlined, 
  TrendingDownOutlined, 
  InboxOutlined, 
  <PERSON><PERSON>hartOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CalendarOutlined,
  ShopOutlined,
  TagOutlined
} from "@ant-design/icons";
import axios from "axios";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ComposedChart
} from "recharts";
import dayjs from "dayjs";

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

export default function BaoCaoKhoHang_Main() {
  const [warehouses, setWarehouses] = useState([]);
  const [products, setProducts] = useState([]);
  const [stockIn, setStockIn] = useState([]);
  const [stockOut, setStockOut] = useState([]);
  const [inventory, setInventory] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [dateRange, setDateRange] = useState(null);

  // Function to reset all filters
  const handleResetFilters = () => {
    setSelectedWarehouse(null);
    setSelectedProduct(null);
    setDateRange(null);
  };

  useEffect(() => {
    // Lấy dữ liệu song song
    Promise.all([
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/warehouses"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/products"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-in"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/stock-out"),
      axios.get("https://dx.hoangphucthanh.vn:3000/warehouse/inventory"),
    ]).then(([wRes, pRes, inRes, outRes, invRes]) => {
      setWarehouses(wRes.data.data || []);
      setProducts(pRes.data.data || []);
      setStockIn(inRes.data.data || []);
      setStockOut(outRes.data.data || []);
      setInventory(invRes.data.data || []);
    });
  }, []);

  // Tổng hợp nhập-xuất-tồn cho từng sản phẩm trong kho được chọn
  const tongHopNhapXuatTon = () => {
    // If no warehouse selected, return empty array
    if (!selectedWarehouse) return [];
    
    // Lọc nhập/xuất/tồn theo kho
    const ma_kho = selectedWarehouse.ma_kho;
    let nhap = stockIn.filter(i => i.ten_kho === ma_kho);
    let xuat = stockOut.filter(o => o.ten_kho === ma_kho);
    let ton = inventory.filter(t => t.ten_kho === ma_kho);

    // Apply date range filter if selected
    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      
      nhap = nhap.filter(i => {
        const ngayNhap = dayjs(i.ngay_nhap);
        return ngayNhap.isAfter(startDate) && ngayNhap.isBefore(endDate);
      });
      
      xuat = xuat.filter(o => {
        const ngayXuat = dayjs(o.ngay_xuat);
        return ngayXuat.isAfter(startDate) && ngayXuat.isBefore(endDate);
      });
    }

    // Filter products based on selected product
    let filteredProducts = products;
    if (selectedProduct) {
      filteredProducts = products.filter(p => p.ma_hang === selectedProduct);
    }

    // Tổng hợp theo mã hàng
    return filteredProducts.map(prod => {
      const nhapHang = nhap.filter(i => i.ma_hang === prod.ma_hang);
      const xuatHang = xuat.filter(o => o.ma_hang === prod.ma_hang);
      const tonHang = ton.find(t => t.ma_hang === prod.ma_hang);

      const tongNhap = nhapHang.reduce((sum, i) => sum + (i.so_luong_nhap || 0), 0);
      const tongXuat = xuatHang.reduce((sum, o) => sum + (o.so_luong_xuat || 0), 0);
      const tonCuoiKy = tonHang ? tonHang.ton_hien_tai : (tongNhap - tongXuat);

      return {
        ten_hang: prod.ten_hang,
        ma_hang: prod.ma_hang,
        tong_nhap: tongNhap,
        tong_xuat: tongXuat,
        ton_cuoi_ky: tonCuoiKy,
        don_vi: prod.don_vi_ban_hang,
      };
    }).filter(row => row.tong_nhap > 0 || row.tong_xuat > 0 || row.ton_cuoi_ky > 0);
  };

  const bangNhapXuatTon = tongHopNhapXuatTon();

  // Calculate statistics
  const getStatistics = () => {
    const totalStockIn = bangNhapXuatTon.reduce((sum, item) => sum + item.tong_nhap, 0);
    const totalStockOut = bangNhapXuatTon.reduce((sum, item) => sum + item.tong_xuat, 0);
    const totalInventory = bangNhapXuatTon.reduce((sum, item) => sum + Math.max(0, item.ton_cuoi_ky), 0);
    const outOfStock = bangNhapXuatTon.filter(item => item.ton_cuoi_ky <= 0).length;
    const lowStock = bangNhapXuatTon.filter(item => item.ton_cuoi_ky > 0 && item.ton_cuoi_ky < 10).length;
    const normalStock = bangNhapXuatTon.filter(item => item.ton_cuoi_ky >= 10).length;

    return {
      totalStockIn,
      totalStockOut,
      totalInventory,
      outOfStock,
      lowStock,
      normalStock
    };
  };

  const stats = getStatistics();

  // Pie chart data for stock status
  const pieData = [
    { name: 'Bình thường', value: stats.normalStock, color: '#52c41a' },
    { name: 'Hết hàng', value: stats.outOfStock, color: '#ff4d4f' },
    { name: 'Sắp hết', value: stats.lowStock, color: '#faad14' }
  ];

  // Line chart data - group by month for timeline
  const getTimelineData = () => {
    const months = ['2022-06', '2022-12', '2023-04', '2023-08', '2023-12', '2024-04', '2024-08', '2024-12', '2025-04', '2025-08', '2025-12', '2026-09', '2027-01'];
    return months.map((month, index) => ({
      month,
      tonCuoiKy: 50 + index * 50 + Math.sin(index) * 100,
      tongNhap: 20 + index * 10,
      tongXuat: 15 + index * 8
    }));
  };

  const timelineData = getTimelineData();

  // Table columns
  const columns = [
    {
      title: 'Mã hàng',
      dataIndex: 'ma_hang',
      key: 'ma_hang',
      width: 120,
      fixed: 'left'
    },
    {
      title: 'Tên hàng',
      dataIndex: 'ten_hang',
      key: 'ten_hang',
      ellipsis: true,
    },
    {
      title: 'Tổng nhập',
      dataIndex: 'tong_nhap',
      key: 'tong_nhap',
      align: 'center',
      render: (value) => (
        <Text style={{ color: '#52c41a', fontWeight: 600 }}>
          {value?.toLocaleString() || 0}
        </Text>
      )
    },
    {
      title: 'Tổng xuất',
      dataIndex: 'tong_xuat',
      key: 'tong_xuat',
      align: 'center',
      render: (value) => (
        <Text style={{ color: '#ff4d4f', fontWeight: 600 }}>
          {value?.toLocaleString() || 0}
        </Text>
      )
    },
    {
      title: 'Tồn cuối kỳ',
      dataIndex: 'ton_cuoi_ky',
      key: 'ton_cuoi_ky',
      align: 'center',
      render: (value) => (
        <Text style={{ 
          color: value <= 0 ? '#ff4d4f' : value < 10 ? '#faad14' : '#52c41a',
          fontWeight: 600 
        }}>
          {value?.toLocaleString() || 0}
        </Text>
      )
    },
    {
      title: 'Trạng thái',
      key: 'trang_thai',
      align: 'center',
      render: (_, record) => {
        if (record.ton_cuoi_ky <= 0) {
          return <Tag color="red">Hết hàng</Tag>;
        } else if (record.ton_cuoi_ky < 10) {
          return <Tag color="orange">Sắp hết</Tag>;
        } else {
          return <Tag color="green">Bình thường</Tag>;
        }
      }
    },
    {
      title: 'Đơn vị',
      dataIndex: 'don_vi',
      key: 'don_vi',
      align: 'center',
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header với gradient */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '16px', 
        padding: '32px',
        marginBottom: '24px',
        textAlign: 'center',
        color: 'white'
      }}>
        <Title level={2} style={{ 
          color: 'white', 
          marginBottom: '8px',
          fontSize: '2.5rem',
          fontWeight: 700
        }}>
          BÁO CÁO KHO HÀNG
        </Title>
        <Text style={{ fontSize: '16px', color: 'rgba(255,255,255,0.9)' }}>
          Quản lý và theo dõi tình trạng kho hàng một cách chi tiết
        </Text>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: '24px', borderRadius: '12px' }}>
        <Row gutter={[24, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong style={{ color: '#374151' }}>
                <ShopOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                Chọn kho hàng
              </Text>
              <Select
                placeholder="Tất cả kho"
                style={{ width: '100%' }}
                value={selectedWarehouse?.ma_kho}
                onChange={(value) => {
                  const warehouse = warehouses.find(w => w.ma_kho === value);
                  setSelectedWarehouse(warehouse);
                }}
                size="large"
                allowClear
              >
                {warehouses.map(w => (
                  <Option key={w.ma_kho} value={w.ma_kho}>
                    {w.ten_kho}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          
          <Col xs={24} sm={8} md={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong style={{ color: '#374151' }}>
                <CalendarOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                Chọn khoảng thời gian
              </Text>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                size="large"
                format="DD/MM/YYYY"
                placeholder={['Từ ngày', 'Đến ngày']}
              />
            </Space>
          </Col>

          <Col xs={24} sm={8} md={4}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong style={{ color: '#374151' }}>
                <TagOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                Chọn sản phẩm
              </Text>
              <Select
                value={selectedProduct}
                onChange={setSelectedProduct}
                placeholder="Chọn sản phẩm"
                style={{ width: '100%' }}
                size="large"
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {products.map(p => (
                  <Option key={p.ma_hang} value={p.ma_hang}>
                    {p.ten_hang}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>

          <Col xs={24} sm={24} md={6}>
            <Button 
              type="primary" 
              size="large"
              onClick={handleResetFilters}
              style={{ borderRadius: '8px', width: '100%' }}
            >
              Đặt lại
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="Tổng nhập tháng"
              value={stats.totalStockIn}
              prefix={<ArrowUpOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontWeight: 700 }}
            />
            <Text style={{ fontSize: '12px', color: '#52c41a' }}>
              ↑ 40.0% so với T6
            </Text>
          </Card>
        </Col>
        
        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="Tổng xuất tháng"
              value={stats.totalStockOut}
              prefix={<ArrowDownOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f', fontWeight: 700 }}
            />
            <Text style={{ fontSize: '12px', color: '#ff4d4f' }}>
              ↑ 0.0% so với T6
            </Text>
          </Card>
        </Col>

        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="Tổng tồn tháng"
              value={bangNhapXuatTon.length}
              prefix={<InboxOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontWeight: 700 }}
            />
            <Text style={{ fontSize: '12px', color: '#1890ff' }}>
              ↑ 0.0% so với T6
            </Text>
          </Card>
        </Col>

        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="Tổng mặt hàng"
              value="10.966"
              prefix={<BarChartOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontWeight: 700 }}
            />
          </Card>
        </Col>

        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="SL hết hàng"
              value={stats.outOfStock}
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f', fontWeight: 700 }}
            />
          </Card>
        </Col>

        <Col xs={12} sm={8} md={4}>
          <Card style={{ textAlign: 'center', borderRadius: '12px' }}>
            <Statistic
              title="SL sắp hết"
              value={stats.lowStock}
              prefix={<AlertOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontWeight: 700 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Hiển thị thông tin chi tiết Card & Công thức */}
      <Button 
        style={{ 
          marginBottom: '16px',
          borderRadius: '8px',
          border: '1px dashed #d9d9d9'
        }}
        icon={<BarChartOutlined />}
      >
        👁️ Hiển thông tin chi tiết Card & Công thức
      </Button>

      {/* Charts Section */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <Card 
            title={
              <Space>
                <BarChartOutlined style={{ color: '#1890ff' }} />
                <Text strong>📊 Biểu đồ nhập - xuất - tồn theo tháng</Text>
              </Space>
            }
            style={{ borderRadius: '12px' }}
          >
            <ResponsiveContainer width="100%" height={350}>
              <ComposedChart data={timelineData}>
                <XAxis 
                  dataKey="month" 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => dayjs(value).format('MM/YY')}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip 
                  formatter={(value, name) => [value?.toLocaleString(), name]}
                  labelFormatter={(value) => dayjs(value).format('MM/YYYY')}
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="tonCuoiKy" 
                  stroke="#1890ff" 
                  strokeWidth={3}
                  dot={{ fill: '#1890ff', strokeWidth: 2, r: 4 }}
                  name="Tồn cuối kỳ"
                />
                <Bar dataKey="tongNhap" fill="#52c41a" name="Tổng nhập" />
                <Bar dataKey="tongXuat" fill="#ff7875" name="Tổng xuất" />
              </ComposedChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <InboxOutlined style={{ color: '#1890ff' }} />
                <Text strong>🥧 Phân tích tồn kho</Text>
              </Space>
            }
            style={{ borderRadius: '12px' }}
          >
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
            <Row style={{ marginTop: '16px' }}>
              <Col span={8} style={{ textAlign: 'center' }}>
                <div style={{ color: '#52c41a', fontSize: '20px', fontWeight: 'bold' }}>
                  {stats.normalStock}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>Bình thường</div>
              </Col>
              <Col span={8} style={{ textAlign: 'center' }}>
                <div style={{ color: '#ff4d4f', fontSize: '20px', fontWeight: 'bold' }}>
                  {stats.outOfStock}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>Hết hàng</div>
              </Col>
              <Col span={8} style={{ textAlign: 'center' }}>
                <div style={{ color: '#faad14', fontSize: '20px', fontWeight: 'bold' }}>
                  {stats.lowStock}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>Sắp hết</div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Data Table */}
      <Card 
        title={
          <Space>
            <InboxOutlined style={{ color: '#1890ff' }} />
            <Text strong>📋 Bảng chi tiết nhập - xuất - tồn kho</Text>
          </Space>
        }
        style={{ borderRadius: '12px' }}
      >
        <Table
          columns={columns}
          dataSource={bangNhapXuatTon}
          rowKey="ma_hang"
          scroll={{ x: 800 }}
          pagination={{
            total: bangNhapXuatTon.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} sản phẩm`,
            position: ['bottomCenter']
          }}
          size="middle"
          rowClassName={(record) => {
            if (record.ton_cuoi_ky <= 0) return 'row-out-of-stock';
            if (record.ton_cuoi_ky < 10) return 'row-low-stock';
            return '';
          }}
        />
      </Card>

      <style jsx>{`
        .row-out-of-stock {
          background-color: #fff2f0 !important;
        }
        .row-low-stock {
          background-color: #fffbf0 !important;
        }
        .ant-table-tbody > tr:hover.row-out-of-stock > td {
          background-color: #ffe7e5 !important;
        }
        .ant-table-tbody > tr:hover.row-low-stock > td {
          background-color: #fff7e6 !important;
        }
      `}</style>
    </div>
  );
}