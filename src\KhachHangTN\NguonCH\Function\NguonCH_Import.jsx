import React, { useState, useEffect } from 'react';
import { Upload, Button, message, Table, Modal, Alert, Typography, Divider, Spin, Badge } from 'antd';
import { InboxOutlined, FileExcelOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import { handleFileUpload } from '../../../utils/import/handleFileUpload';
import { checkDuplicateInFile, validateData, getFieldLabel } from '../../../utils/import/validationHelpers';
import { downloadTemplate } from '../../../utils/import/templateHelpers';
import TemplateDownloadSection from '../../../utils/import/templateDownloadSection';
import {
  fetchPreviewData,
  renderMaNguon,
  renderNguoiCapNhat,
  isNguonCHExisting,
} from './NguonCH_ImportRender';
import renderPreview from '../../../utils/import/renderPreview';
import { crmInstance } from '../../../utils/api/axiosConfig';

const { Dragger } = Upload;

const NguonCH_Import = ({ open, onClose, onSuccess, disabled }) => {
  // State quản lý dữ liệu
  const [existingNguonCH, setExistingNguonCH] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [parsedData, setParsedData] = useState([]);
  const [importLoading, setImportLoading] = useState(false);
  const [errorItems, setErrorItems] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  // Add resetState function
  const resetState = () => {
    setFileList([]);
    setParsedData([]);
    setErrorItems([]);
    setShowPreview(false);
  };

  // Add handleClose function
  const handleClose = () => {
    resetState();
    onClose();
  };

  // Hàm tự động tạo mã nguồn cơ hội mới
  const generateMaNguon = (() => {
    let base = null;
    return (index = 0) => {
      if (base === null) {
        let maxNumber = 0;
        existingNguonCH.forEach(item => {
          if (item.ma_nguon) {
            const match = item.ma_nguon.match(/^CH(\d+)$/);
            if (match) {
              const num = parseInt(match[1], 10);
              if (num > maxNumber) maxNumber = num;
            }
          }
        });
        base = maxNumber;
      }
      return `CH${String(base + index + 1).padStart(2, '0')}`;
    };
  })();

  useEffect(() => {
    fetchPreviewData(setAccounts, setExistingNguonCH);
    resetState();
  }, [open]);

  // Mapping giữa tiêu đề cột Excel và các trường API
  const columnMapping = {
    'Mã nguồn': 'ma_nguon',
    'Tên nguồn': 'nguon',
    'Người cập nhật': 'nguoi_cap_nhat',
  };

  // Các trường bắt buộc
  const requiredFields = ['nguon', 'nguoi_cap_nhat']; // Removed ma_nguon as required since we'll auto-generate
  const uniqueFields = ['ma_nguon'];

  const handleAfterParse = (parsedRows) => {
    // Auto-generate missing or invalid codes
    let nextIndex = 0;
    const processedData = parsedRows.map((item, index) => {
      const processedItem = { ...item, key: index };
      
      // Check if ma_nguon is missing, empty, or already exists
      if (!item.ma_nguon || item.ma_nguon.trim() === '' || isNguonCHExisting(item.ma_nguon, existingNguonCH)) {
        processedItem.ma_nguon = generateMaNguon(nextIndex++);
        processedItem.autoGenerated = true;
      }
      
      return processedItem;
    });

    setParsedData(processedData);
    handleValidateData(processedData);
    setShowPreview(true);
  };

  // Hàm xác thực dữ liệu
  const handleValidateData = (data) => {
    // Kiểm tra trùng trong file
    const duplicates = checkDuplicateInFile(data, uniqueFields);

    // Modify validation for auto-generated codes
    const customValidations = {
      ma_nguon: (value, item) => {
        // Skip validation for auto-generated codes
        if (item.autoGenerated) return false;
        return isNguonCHExisting(value, existingNguonCH);
      }
    };

    return validateData(
      data,
      requiredFields,
      (field) => getFieldLabel(field, columnMapping),
      setErrorItems,
      'ma_nguon',
      'ma_nguon',
      customValidations,
      duplicates
    );
  };

  // Hàm chuẩn bị dữ liệu để gửi
  const prepareDataForImport = (data) => {
    return data.map(item => ({
      ...item,
      ngay_cap_nhat: item.ngay_cap_nhat
        ? moment(item.ngay_cap_nhat).format('YYYY-MM-DD')
        : moment().format('YYYY-MM-DD'), // Default to today
      autoGenerated: undefined // Remove this field before sending to API
    }));
  };

  // Hàm nhập từng dòng
  const importSingleItem = async (item) => {
    try {
      const response = await crmInstance.post('/opportunity-sources', item);
      return response.status < 400;
    } catch (error) {
      console.error('Lỗi khi nhập từng item:', error);
      return false;
    }
  };

  // Hàm nhập toàn bộ dữ liệu
  const importAllItems = async (data) => {
    try {
      const response = await crmInstance.post('/opportunity-sources/batch', data);
      return response.status < 400;
    } catch (error) {
      console.error('Error importing data:', error);
      throw error;
    }
  };

  // Hàm xử lý nhập dữ liệu
  const handleImport = async () => {
    if (errorItems.length > 0) {
      message.error('Vui lòng sửa lỗi trước khi nhập dữ liệu!');
      return;
    }

    if (parsedData.length === 0) {
      message.warning('Không có dữ liệu để nhập!');
      return;
    }

    setImportLoading(true);

    try {
      const dataToImport = prepareDataForImport(parsedData);
      console.log('Data to import:', dataToImport);

      // Thử nhập toàn bộ dữ liệu
      const success = await importAllItems(dataToImport);

      if (success) {
        message.success(`Đã nhập ${dataToImport.length} nguồn cơ hội thành công!`);
        fetchPreviewData(setAccounts, setExistingNguonCH);
        resetState();
        onSuccess?.();
        onClose();
        return;
      }

      throw new Error('Có lỗi xảy ra khi nhập dữ liệu');
    } catch (error) {
      message.error(`Không thể nhập dữ liệu: ${error.message}`);
      message.info('Thử một cách khác - tạo từng nguồn cơ hội một...');

      // Thử nhập từng dòng
      let successCount = 0;
      for (const item of prepareDataForImport(parsedData)) {
        const success = await importSingleItem(item);
        if (success) successCount++;
      }

      if (successCount > 0) {
        message.success(`Đã nhập ${successCount}/${parsedData.length} nguồn cơ hội thành công!`);
      } else {
        message.error('Không thể nhập được nguồn cơ hội nào!');
      }
      fetchPreviewData(setAccounts, setExistingNguonCH);
      resetState();
      onSuccess?.();
      onClose();
    } finally {
      setImportLoading(false);
    }
  };

  // Add renderTemplateSection function
  const renderTemplateSection = () => (
    <TemplateDownloadSection 
      handleDownloadTemplate={() => {
        const columns = Object.keys(columnMapping);
        const sampleData = [
          ['CH01', 'Facebook', 'VTTphuong'],
          ['CH02', 'Google Ads', 'PPcuong']
        ];
        downloadTemplate(columns, sampleData, 'Template_Nguon_Co_Hoi');
      }} 
    />
  );

  // Add renderPreviewSection function
  const renderPreviewSection = () => {
    // Cấu hình cột cho bảng xem trước dữ liệu
    const previewColumns = [
      { title: 'STT', dataIndex: 'key', key: 'key', width: "5%",
        render: (text) => text + 1 
      },
      { 
        title: 'Mã nguồn', 
        dataIndex: 'ma_nguon', 
        key: 'ma_nguon', 
        width: "15%",
        render: (text, record) => renderMaNguon(text, record, errorItems, existingNguonCH)
      },
      { 
        title: 'Tên nguồn', 
        dataIndex: 'nguon', 
        key: 'nguon', 
        width: "40%",
      },
      {
        title: 'Người cập nhật',
        dataIndex: 'nguoi_cap_nhat',
        key: 'nguoi_cap_nhat',
        width: "40%",
        render: (maNguoiDung, record) => renderNguoiCapNhat(maNguoiDung, record, accounts, errorItems)
      }
    ];

    return renderPreview({
      label: "Tổng số nguồn cơ hội",
      dataSource: parsedData,
      columns: previewColumns,
      errorItems,
      onCancel: resetState,
      onImport: handleImport,
      importLoading,
      hasErrors: errorItems.length > 0,
      scrollX: 800,
      pageSize: 10,
      getErrorTitle: (item) => `Hàng ${item.index + 1}`,
      getErrorDescription: (item) => item.errors.join(', '),
      disabled,
    });
  };

  return (
    <Modal
      className="import-modal"
      title={
        <div className="import-modal-title">
          <UploadOutlined /> Nhập danh sách nguồn cơ hội từ Excel
        </div>
      }
      open={open}
      onCancel={handleClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <Spin spinning={importLoading} tip="Đang nhập dữ liệu...">
        <div className="import-container">
          {!showPreview && (
            <Alert
              message="Hướng dẫn nhập dữ liệu"
              description={
                <ol>
                  <li>Tải xuống file mẫu Excel hoặc sử dụng file có cấu trúc tương tự.</li>
                  <li>Điền thông tin nguồn cơ hội vào file (mỗi dòng là một nguồn).</li>
                  <li>Tải lên file Excel đã điền thông tin.</li>
                  <li>Kiểm tra dữ liệu xem trước và sửa các lỗi nếu có.</li>
                  <li>Nhấn "Nhập dữ liệu" để hoàn tất.</li>
                  <li>Các trường bắt buộc: Tên nguồn, Người cập nhật.</li>
                  <li>Mã nguồn sẽ được tự động tạo nếu bỏ trống hoặc trùng với mã đã tồn tại.</li>
                </ol>
              }
              type="info"
              showIcon
            />
          )}

          <div className="import-content">
            {!showPreview ? (
              <>
                {renderTemplateSection()}
                <Divider />
                <Dragger
                  name="file"
                  multiple={false}
                  fileList={fileList}
                  beforeUpload={(file) => handleFileUpload(file, {
                    columnMapping,
                    setParsedData: handleAfterParse, // Use the new function
                    validateData: () => {}, // Validation happens in handleAfterParse
                    setShowPreview,
                    setFileList,
                    accounts,
                    defaultFields: { ngay_cap_nhat: moment().format('YYYY-MM-DD') },
                    mode: 'nguonCH'
                  })}
                  onRemove={() => resetState()}
                  accept=".xlsx,.xls"
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">Kéo thả file hoặc Click để chọn file Excel</p>
                  <p className="ant-upload-hint">
                    Chỉ hỗ trợ file Excel (.xlsx, .xls)
                  </p>
                </Dragger>
              </>
            ) : (
              renderPreviewSection()
            )}
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default NguonCH_Import;