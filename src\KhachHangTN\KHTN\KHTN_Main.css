/* Container chính */
.bang-khach-hang-container {
    padding: 1rem;
    background-color: #f4f6f8;
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Vùng cuộn ngang cho bảng */
.bang-khach-hang-scroll-wrapper {
    width: 100%;
    overflow-x: auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Responsive styling cho các filters */
.custom-filter-card {
    margin-bottom: 1rem;
}

.custom-filter-card .ant-card-body {
    padding: 16px;
}

/* Style cho các input và select trong filter */
.custom-filter-card .ant-input-affix-wrapper,
.custom-filter-card .ant-select,
.custom-filter-card .ant-btn {
    width: 100%;
}

/* Responsive để các button vừa với container */
.add_update-modal .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Custom scrollbar */
.bang-khach-hang-scroll-wrapper::-webkit-scrollbar {
    height: 8px;
}

.bang-khach-hang-scroll-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.bang-khach-hang-scroll-wrapper::-webkit-scrollbar-thumb {
    background: #bfc4c9;
    border-radius: 4px;
}

.bang-khach-hang-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a0a4a8;
}

/* Media queries cho responsive design */
@media (max-width: 1200px) {
    .bang-khach-hang-container {
        padding: 0.75rem;
    }
}

@media (max-width: 768px) {
    .bang-khach-hang-container {
        padding: 0.5rem;
    }
    
    .add_update-modal .form-actions {
        flex-direction: column;
    }
    
    .add_update-modal .form-actions button {
        width: 100%;
    }
}

/* Make table more responsive */
.custom-ant-table .ant-table-cell {
    white-space: nowrap;
    padding: 8px;
}

/* Highlight rows on hover */
.custom-ant-table .ant-table-tbody > tr:hover > td {
    background-color: #f0f7ff !important;
}

/* Style pagination to be more visible */
.ant-pagination {
    margin: 16px 0;
    text-align: right;
}

/* Style modal headers */
.add_update-modal .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
}

.edit-title {
    font-size: 1.25rem;
    color: #1890ff;
    margin-bottom: 1.5rem;
    font-weight: 500;
}