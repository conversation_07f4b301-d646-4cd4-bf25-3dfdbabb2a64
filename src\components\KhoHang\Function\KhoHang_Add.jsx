import React, { useState, useEffect } from "react";
import { createWarehouse, getWarehouses, getAccountList } from "./khoHangApi";
import {
  Form, Button, Input, Typography, Select, AutoComplete, DatePicker, InputNumber, message
} from "antd";
import dayjs from "dayjs/esm/index.js";

const { Title } = Typography;
const { Option } = Select;

const init = {
  ma_kho: "",
  ten_kho: "",
  vi_tri_kho: "",
  tinh_trang: "Đang hoạt động",
  quan_ly_kho: "", // sẽ là MaNguoiDung của người quản lý kho
  ngay_kiem_ke_gan_nhat: "",
  tong_gia_tri_nhap: 0,
  tong_gia_tri_xuat: 0,
  tong_gia_tri_ton_kho: 0,
  ghi_chu: "",
  hinh_anh: "", // đường dẫn hoặc base64 ảnh
};

function getNextMaKho(list) {
  const arr = list.map((kho) => kho.ma_kho || "").filter(Boolean);
  if (!arr.length) return "K01";
  const numbers = arr.map((ma) => Number(ma.replace(/[^\d]/g, "")) || 0);
  const max = Math.max(...numbers);
  return `K${(max + 1).toString().padStart(2, "0")}`;
}

export default function KhoHang_Add({ onSuccess, onCancel }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState({ ho_va_ten: "", ma_nguoi_dung: "" });
  const [accounts, setAccounts] = useState([]);
  const [previewImg, setPreviewImg] = useState("");

  useEffect(() => {
    const storedUser = JSON.parse(localStorage.getItem("userData") || "{}");
    setUser({
      ho_va_ten: storedUser.ho_va_ten || storedUser.TenDayDu || "",
      ma_nguoi_dung: storedUser.ma_nguoi_dung || storedUser.MaNguoiDung || "",
    });

    async function fetchAndSet() {
      setLoading(true);
      try {
        const res = await getWarehouses();
        const list = Array.isArray(res.data) ? res.data : (res.data?.data || []);
        const nextMaKho = getNextMaKho(list);
        
        form.setFieldsValue({
          ...init,
          ma_kho: nextMaKho,
          nguoi_tao: storedUser.ho_va_ten || storedUser.TenDayDu || "",
        });

        // Lấy danh sách user (Accounts)
        const resAcc = await getAccountList();
        const dataAcc = Array.isArray(resAcc.data?.data) ? resAcc.data.data : [];
        setAccounts(dataAcc);
      } finally {
        setLoading(false);
      }
    }
    fetchAndSet();
  }, [form]);

  const handleSubmit = async (values) => {
    if (!values.quan_ly_kho) {
      message.error("Vui lòng chọn người quản lý kho!");
      return;
    }
    
    const body = {
      ...values,
      nguoi_tao: user.ma_nguoi_dung,
      ngay_tao: dayjs().format("DD/MM/YYYY"), // chuẩn Việt Nam
      ngay_kiem_ke_gan_nhat: values.ngay_kiem_ke_gan_nhat
        ? dayjs(values.ngay_kiem_ke_gan_nhat).format("DD/MM/YYYY")
        : undefined,
      tong_gia_tri_nhap: Number(values.tong_gia_tri_nhap) || 0,
      tong_gia_tri_xuat: Number(values.tong_gia_tri_xuat) || 0,
      tong_gia_tri_ton_kho: Number(values.tong_gia_tri_ton_kho) || 0,
    };

    try {
      await createWarehouse(body);
      message.success("Thêm kho thành công!");
      onSuccess();
    } catch (err) {
      if (err.response) {
        console.log("API Error:", err.response.data);
        message.error("Có lỗi xảy ra khi thêm kho!");
      }
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={4}>Thêm kho mới</Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={init}
      >
        <Form.Item
          name="ma_kho"
          label="Mã kho"
          rules={[{ required: true, message: 'Mã kho là bắt buộc!' }]}
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          name="ten_kho"
          label="Tên kho"
          rules={[{ required: true, message: 'Tên kho là bắt buộc!' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="vi_tri_kho"
          label="Vị trí kho"
          rules={[{ required: true, message: 'Vị trí kho là bắt buộc!' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="tinh_trang"
          label="Tình trạng"
          rules={[{ required: true, message: 'Tình trạng là bắt buộc!' }]}
        >
          <Select>
            <Option value="Đang hoạt động">Đang hoạt động</Option>
            <Option value="Bảo trì">Bảo trì</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Người tạo"
        >
          <Input value={user.ho_va_ten} disabled />
        </Form.Item>

        <Form.Item
          name="quan_ly_kho"
          label="Quản lý kho"
          rules={[{ required: true, message: 'Quản lý kho là bắt buộc!' }]}
        >
          <Select
            showSearch
            placeholder="Chọn người quản lý kho"
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {accounts.map(acc => (
              <Option key={acc.ma_nguoi_dung} value={acc.ma_nguoi_dung}>
                {acc.ho_va_ten}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="ngay_kiem_ke_gan_nhat"
          label="Ngày kiểm kê gần nhất"
        >
          <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_nhap"
          label="Tổng nhập"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_xuat"
          label="Tổng xuất"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="tong_gia_tri_ton_kho"
          label="Tổng tồn kho"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="ghi_chu"
          label="Ghi chú"
        >
          <Input.TextArea rows={3} />
        </Form.Item>

        <Form.Item style={{ marginTop: 16 }}>
          <Button type="primary" htmlType="submit" loading={loading} style={{ marginRight: 8 }}>
            Thêm
          </Button>
          <Button onClick={onCancel}>
            Hủy
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
