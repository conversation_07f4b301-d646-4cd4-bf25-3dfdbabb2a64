import { Button, Space } from 'antd';
import { formatDate } from '../../../utils/format/formatDate';

export const getNhapKhoColumns = (handleEdit, handleRemove, canEdit) => [
    {
        title: 'STT',
        dataIndex: 'stt',
        key: 'stt',
        width: 60,
        fixed: 'left',
        align: 'center'
    },
    {
        title: 'Mã hàng',
        dataIndex: 'ma_hang',
        key: 'ma_hang',
        width: 120,
        sorter: (a, b) => (a.ma_hang || '').localeCompare(b.ma_hang || ''),
        ellipsis: true
    },
    {
        title: 'Ng<PERSON>y nhập',
        dataIndex: 'ngay_nhap_hang',
        key: 'ngay_nhap_hang',
        render: (text) => formatDate(text),
        width: 110,
        sorter: (a, b) => new Date(a.ngay_nhap_hang) - new Date(b.ngay_nhap_hang),
        defaultSortOrder: 'descend',
        align: 'center'
    },
    {
        title: '<PERSON><PERSON> lượng',
        dataIndex: 'so_luong_nhap',
        key: 'so_luong_nhap',
        width: 100,
        align: 'right',
        sorter: (a, b) => (Number(a.so_luong_nhap) || 0) - (Number(b.so_luong_nhap) || 0)
    },
    {
        title: 'Nhà cung cấp',
        key: 'ten_nha_cung_cap',
        width: 200,
        render: (_, record) => record.product?.suppliers?.ten_nha_cung_cap,
        sorter: (a, b) => {
            const aName = a.product?.suppliers?.ten_nha_cung_cap || '';
            const bName = b.product?.suppliers?.ten_nha_cung_cap || '';
            return aName.localeCompare(bName, 'vi');
        },
        ellipsis: true
    },
    {
        title: 'Kho',
        dataIndex: ['warehouse', 'ten_kho'],
        key: 'ten_kho',
        width: 120,
        sorter: (a, b) => {
            const aName = a.warehouse?.ten_kho || '';
            const bName = b.warehouse?.ten_kho || '';
            return aName.localeCompare(bName, 'vi');
        },
        ellipsis: true
    },
    {
        title: 'Bill',
        dataIndex: 'ma_bill',
        key: 'ma_bill',
        width: 100,
        sorter: (a, b) => (a.ma_bill || '').localeCompare(b.ma_bill || ''),
        ellipsis: true
    },
    {
        title: 'Hợp Đồng',
        dataIndex: 'ma_hop_dong',
        key: 'ma_hop_dong',
        width: 140,
        sorter: (a, b) => (a.ma_hop_dong || '').localeCompare(b.ma_hop_dong || ''),
        ellipsis: true
    },
    {
        title: 'Hành động',
        key: 'hanh_dong',
        width: 140,
        fixed: 'right',
        align: 'center',
        render: (_, record) => (
            <Space>
                <Button
                    type="primary"
                    size="small"
                    disabled={!canEdit}
                    onClick={() => handleEdit(record)}
                    style={{ fontSize: '11px' }}
                >
                    Sửa
                </Button>
                <Button
                    type="primary"
                    danger
                    size="small"
                    disabled={!canEdit}
                    onClick={() => handleRemove(record)}
                    style={{ fontSize: '11px' }}
                >
                    Xóa
                </Button>
            </Space>
        )
    },
];
