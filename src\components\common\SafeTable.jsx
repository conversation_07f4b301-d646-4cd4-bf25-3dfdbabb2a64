import React, { useEffect, useRef } from 'react';
import { Table } from 'antd';

// Safe Table wrapper để xử lý ResizeObserver errors
const SafeTable = (props) => {
  const tableRef = useRef(null);

  useEffect(() => {
    // Suppress ResizeObserver errors specifically for this table
    const originalError = console.error;
    
    const handleTableError = (...args) => {
      const message = args[0];
      if (typeof message === 'string' && 
          (message.includes('ResizeObserver') || 
           message.includes('undelivered notifications') ||
           message.includes('handleError'))) {
        return; // Suppress ResizeObserver errors
      }
      originalError.apply(console, args);
    };

    // Temporarily override console.error for this component
    console.error = handleTableError;

    // Cleanup function
    return () => {
      console.error = originalError;
    };
  }, []);

  // Error boundary for ResizeObserver
  useEffect(() => {
    const handleError = (event) => {
      if (event.error?.message?.includes('ResizeObserver') ||
          event.message?.includes('ResizeObserver')) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    };

    window.addEventListener('error', handleError, true);
    
    return () => {
      window.removeEventListener('error', handleError, true);
    };
  }, []);

  // Wrap table in error boundary
  try {
    return (
      <div ref={tableRef} className="safe-table-wrapper">
        <Table {...props} />
      </div>
    );
  } catch (error) {
    if (error.message?.includes('ResizeObserver')) {
      // Fallback render without error
      return (
        <div ref={tableRef} className="safe-table-wrapper">
          <Table {...props} />
        </div>
      );
    }
    throw error; // Re-throw non-ResizeObserver errors
  }
};

export default SafeTable;
