import React from 'react';
import { Typography, Row, Col } from 'antd';
import { FaSmog , FaThermometerHalf, FaTint } from 'react-icons/fa';

const features = [
  { icon: <FaSmog  size={48} />, title: 'Pollution', description: 'Monitor pollution in real-time.' },
  { icon: <FaThermometerHalf size={48} />, title: 'Temperature', description: 'Get accurate temperature readings.' },
  { icon: <FaTint size={48} />, title: 'Humidity', description: 'Check the humidity levels instantly.' },
];

const Features = () => (
  <div style={{ padding: '50px 0', backgroundColor: '#ffffff' }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
      <Typography.Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
        Features
      </Typography.Title>
      <Row gutter={[32, 32]}>
        {features.map((feature, index) => (
          <Col xs={24} md={8} key={index}>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              {feature.icon}
              <Typography.Title level={4} style={{ marginTop: '16px', marginBottom: '8px' }}>
                {feature.title}
              </Typography.Title>
              <Typography.Text>{feature.description}</Typography.Text>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  </div>
);

export default Features;
