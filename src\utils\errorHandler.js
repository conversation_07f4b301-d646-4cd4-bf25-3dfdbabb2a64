// Utility để xử lý lỗi ResizeObserver
export const suppressResizeObserverError = () => {
  // Lưu trữ handler lỗi gốc
  const originalError = console.error;
  
  // Override console.error để bỏ qua lỗi ResizeObserver
  console.error = (...args) => {
    // Kiểm tra nếu là lỗi ResizeObserver thì bỏ qua
    if (
      args.length > 0 &&
      typeof args[0] === 'string' &&
      args[0].includes('ResizeObserver loop completed with undelivered notifications')
    ) {
      return;
    }
    
    // Gọi console.error gốc cho các lỗi khác
    originalError.apply(console, args);
  };
};

// Debounce function để giảm tần suất gọi ResizeObserver
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Error boundary component
export class ResizeObserverErrorBoundary extends Error {
  constructor(message) {
    super(message);
    this.name = 'ResizeObserverError';
  }
}

// Wrapper cho ResizeObserver với error handling
export const createSafeResizeObserver = (callback) => {
  const debouncedCallback = debounce(callback, 16); // 16ms ≈ 60fps
  
  return new ResizeObserver((entries, observer) => {
    try {
      debouncedCallback(entries, observer);
    } catch (error) {
      if (error.message.includes('ResizeObserver loop completed')) {
        // Bỏ qua lỗi ResizeObserver loop
        return;
      }
      // Re-throw các lỗi khác
      throw error;
    }
  });
};

// Polyfill cho ResizeObserver nếu không được hỗ trợ
export const polyfillResizeObserver = () => {
  if (typeof window !== 'undefined' && !window.ResizeObserver) {
    window.ResizeObserver = class ResizeObserver {
      constructor(callback) {
        this.callback = callback;
        this.observedElements = new Set();
      }
      
      observe(element) {
        this.observedElements.add(element);
        // Fallback: sử dụng window resize event
        window.addEventListener('resize', this.handleResize);
      }
      
      unobserve(element) {
        this.observedElements.delete(element);
        if (this.observedElements.size === 0) {
          window.removeEventListener('resize', this.handleResize);
        }
      }
      
      disconnect() {
        this.observedElements.clear();
        window.removeEventListener('resize', this.handleResize);
      }
      
      handleResize = debounce(() => {
        const entries = Array.from(this.observedElements).map(element => ({
          target: element,
          contentRect: element.getBoundingClientRect()
        }));
        this.callback(entries);
      }, 100);
    };
  }
};
