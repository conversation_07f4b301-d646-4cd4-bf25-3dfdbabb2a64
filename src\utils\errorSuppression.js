// Comprehensive ResizeObserver error suppression
export const suppressResizeObserverErrors = () => {
  // Store the original handlers
  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;

  // Helper to check if error is ResizeObserver related
  const isResizeObserverError = (message) => {
    if (!message) return false;
    const msgStr = typeof message === 'string' ? message : String(message);
    return msgStr.includes('ResizeObserver loop completed with undelivered notifications') ||
           msgStr.includes('ResizeObserver loop limit exceeded') ||
           msgStr.includes('ResizeObserver') ||
           msgStr.includes('handleError') ||
           (msgStr.includes('bundle.js') && msgStr.includes('ResizeObserver'));
  };

  // Check for React Error Overlay and suppress it
  const suppressReactErrorOverlay = () => {
    // Hide React Error Overlay for ResizeObserver errors
    const style = document.createElement('style');
    style.textContent = `
      iframe[data-reactroot] { display: none !important; }
      div[data-reactroot] { display: none !important; }
      .react-error-overlay { display: none !important; }
    `;
    document.head.appendChild(style);

    // Prevent error boundary from showing ResizeObserver errors
    const originalReportError = window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.onCommitFiberRoot;
    if (originalReportError) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = function(...args) {
        try {
          return originalReportError.apply(this, args);
        } catch (error) {
          if (!isResizeObserverError(error?.message)) {
            throw error;
          }
        }
      };
    }
  };

  // Override console methods
  console.error = (...args) => {
    if (args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      return false;
    })) {
      return; // Suppress
    }
    originalError.apply(console, args);
  };

  console.warn = (...args) => {
    if (args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      return false;
    })) {
      return; // Suppress
    }
    originalWarn.apply(console, args);
  };

  console.log = (...args) => {
    if (args.some(arg => {
      if (typeof arg === 'string') return isResizeObserverError(arg);
      if (arg && arg.message) return isResizeObserverError(arg.message);
      if (arg && arg.stack) return isResizeObserverError(arg.stack);
      return false;
    })) {
      return; // Suppress
    }
    originalLog.apply(console, args);
  };

  // Monkey patch ResizeObserver to prevent errors at source
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = ResizeObserver;
    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback) {
        const wrappedCallback = (entries, observer) => {
          try {
            // Use both requestAnimationFrame and setTimeout for better timing
            requestAnimationFrame(() => {
              setTimeout(() => {
                try {
                  callback(entries, observer);
                } catch (error) {
                  if (!isResizeObserverError(error?.message)) {
                    originalError('Non-ResizeObserver error:', error);
                  }
                  // Suppress ResizeObserver errors completely
                }
              }, 0);
            });
          } catch (error) {
            if (!isResizeObserverError(error?.message)) {
              originalError('ResizeObserver constructor error:', error);
            }
            // Suppress ResizeObserver errors completely
          }
        };
        
        super(wrappedCallback);
      }
    };
  }

  // Call React Error Overlay suppression
  suppressReactErrorOverlay();

  // Window error handlers
  const handleWindowError = (event) => {
    const message = event.message || (event.error && event.error.message) || '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  };

  const handleUnhandledRejection = (event) => {
    const message = event.reason && event.reason.message ? event.reason.message : '';
    if (isResizeObserverError(message)) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  };

  const handleGlobalError = (message, source, lineno, colno, error) => {
    if (isResizeObserverError(message) || (error && isResizeObserverError(error.message))) {
      return true; // Prevent default error handling
    }
    return false;
  };

  // Add event listeners with capture phase
  window.addEventListener('error', handleWindowError, true);
  window.addEventListener('unhandledrejection', handleUnhandledRejection, true);
  window.onerror = handleGlobalError;

  // Return cleanup function
  return () => {
    console.error = originalError;
    console.warn = originalWarn;
    console.log = originalLog;
    window.removeEventListener('error', handleWindowError, true);
    window.removeEventListener('unhandledrejection', handleUnhandledRejection, true);
    window.onerror = null;
  };
};
